"""
Strackr API authentication management.
Handles secure credential retrieval and API authentication.
"""

import logging
from google.cloud import secretmanager
from .constants import (
    STRACKR_API_ID_SECRET,
    STRACKR_API_KEY_SECRET,
    STRACKR_BASE_URL
)

logger = logging.getLogger(__name__)

class StrackrAuth:
    """
    Manages Strackr API authentication credentials and parameters.
    """
    
    def __init__(self):
        """Initialize authentication with credentials from Secret Manager."""
        self.secret_client = secretmanager.SecretManagerServiceClient()
        self.base_url = STRACKR_BASE_URL
        self._api_id = None
        self._api_key = None
        
    def _get_secret(self, secret_path: str) -> str:
        """
        Retrieve a secret from Google Secret Manager.
        
        Args:
            secret_path: Full path to the secret
            
        Returns:
            Secret value as string
            
        Raises:
            Exception: If secret cannot be retrieved
        """
        try:
            response = self.secret_client.access_secret_version(
                request={"name": secret_path}
            )
            secret_value = response.payload.data.decode("UTF-8")
            logger.info(f"Successfully retrieved secret: {secret_path.split('/')[-3]}")
            return secret_value
        except Exception as e:
            logger.error(f"Failed to retrieve secret {secret_path}: {str(e)}")
            raise Exception(f"Could not retrieve Strackr API credentials: {str(e)}")
    
    @property
    def api_id(self) -> str:
        """Get API ID, loading from Secret Manager if needed."""
        if self._api_id is None:
            self._api_id = self._get_secret(STRACKR_API_ID_SECRET)
        return self._api_id
    
    @property
    def api_key(self) -> str:
        """Get API Key, loading from Secret Manager if needed."""
        if self._api_key is None:
            self._api_key = self._get_secret(STRACKR_API_KEY_SECRET)
        return self._api_key
    
    def get_auth_params(self) -> dict:
        """
        Get authentication parameters for Strackr API requests.
        
        Returns:
            Dictionary with api_id and api_key for query parameters
        """
        return {
            'api_id': self.api_id,
            'api_key': self.api_key
        }
    
    def get_authenticated_url(self, endpoint: str) -> str:
        """
        Get full URL with authentication parameters.
        
        Args:
            endpoint: API endpoint (e.g., '/im/reports/transaction_inquiries')
            
        Returns:
            Full URL with base URL and endpoint
        """
        return f"{self.base_url}{endpoint}"
    
    def validate_credentials(self) -> bool:
        """
        Validate that credentials are available and properly formatted.
        
        Returns:
            True if credentials are valid, False otherwise
        """
        try:
            api_id = self.api_id
            api_key = self.api_key
            
            if not api_id or not api_key:
                logger.error("API ID or API Key is empty")
                return False
            
            if len(api_id.strip()) == 0 or len(api_key.strip()) == 0:
                logger.error("API ID or API Key contains only whitespace")
                return False
            
            logger.info("Strackr API credentials validated successfully")
            return True
            
        except Exception as e:
            logger.error(f"Credential validation failed: {str(e)}")
            return False
