"""
Transaction Reports Package

This package provides functionality for processing transaction data from multiple
affiliate platforms (Strackr, ShopMy) and normalizing it into a unified schema.

Main Components:
- strackr_client: API client for Strackr transaction inquiries
- strackr_auth: Authentication management for Strackr API
- strackr_transform: Data transformation for Strackr transactions
- schema: Normalized transaction schema and validation
- utils: Common utilities for data processing and storage
- constants: Configuration constants and API endpoints

Usage:
    from dags.dependencies.transaction_reports import (
        StrackrClient,
        NormalizedTransaction,
        transform_strackr_transactions
    )
"""

from .strackr_client import StrackrClient
from .strackr_auth import StrackrAuth
from .strackr_transform import (
    transform_strackr_transaction,
    transform_strackr_transactions,
    get_transformation_summary
)
from .schema import (
    NormalizedTransaction,
    BIGQUERY_SCHEMA,
    validate_transaction,
    is_valid_transaction
)
from .utils import (
    get_date_range_for_yesterday,
    get_date_range_for_days_back,
    store_transactions_to_gcs,
    validate_transaction_data_quality,
    create_processing_summary
)
from .constants import (
    PLATFORM_STRACKR,
    PLATFORM_SHOPMY,
    STRACKR_TABLE_ID,
    SHOPMY_TABLE_ID,
    NORMALIZED_TABLE_ID
)

__version__ = "1.0.0"
__author__ = "Phia Data Team"

__all__ = [
    # Strackr components
    "StrackrClient",
    "StrackrAuth",
    "transform_strackr_transaction",
    "transform_strackr_transactions",
    "get_transformation_summary",
    
    # Schema components
    "NormalizedTransaction",
    "BIGQUERY_SCHEMA",
    "validate_transaction",
    "is_valid_transaction",
    
    # Utility functions
    "get_date_range_for_yesterday",
    "get_date_range_for_days_back",
    "store_transactions_to_gcs",
    "validate_transaction_data_quality",
    "create_processing_summary",
    
    # Constants
    "PLATFORM_STRACKR",
    "PLATFORM_SHOPMY",
    "STRACKR_TABLE_ID",
    "SHOPMY_TABLE_ID",
    "NORMALIZED_TABLE_ID",
]
