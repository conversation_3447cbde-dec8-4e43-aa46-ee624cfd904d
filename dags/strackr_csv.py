"""
Strackr Transaction Reports DAG

This DAG fetches transaction inquiries from Strackr API, transforms them to a normalized schema,
validates the data quality, and stores the results in GCS.

Schedule: Daily at 2 AM UTC
Catchup: False (only process current data)
"""

from airflow import DAG
from airflow.operators.python import Python<PERSON>perator
from datetime import datetime, timedelta
import logging

# Import our custom functions
from dependencies.transaction_reports.strackr_csv import (
    fetch_strackr_transactions_task,
    transform_strackr_data_task,
    validate_and_store_strackr_data_task
)

logger = logging.getLogger(__name__)

# DAG Configuration
default_args = {
    'owner': 'data-team',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'email': ['<EMAIL>'],
}

# Create the DAG
dag = DAG(
    'strackr_transaction_reports',
    default_args=default_args,
    description='Fetch and process Strackr transaction inquiries',
    schedule_interval='0 2 * * *',  # Daily at 2 AM UTC
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=['transaction', 'strackr', 'reports', 'daily'],
    max_active_runs=1,  # Prevent overlapping runs
)

# Task 1: Fetch raw transaction data from Strackr API
fetch_transactions = PythonOperator(
    task_id='fetch_strackr_transactions',
    python_callable=fetch_strackr_transactions_task,
    dag=dag,
    doc_md="""
    ## Fetch Strackr Transactions
    
    This task fetches transaction inquiries from the Strackr API for yesterday's date range.
    
    **What it does:**
    - Connects to Strackr API using stored credentials
    - Fetches transaction inquiries for yesterday (00:00:00 to 23:59:59 UTC)
    - Handles pagination to get all available data
    - Returns raw transaction data for processing
    
    **Output:**
    - Raw transaction count
    - Date range processed
    - Processing time
    - Raw transaction data (passed to next task)
    """,
)

# Task 2: Transform raw data to normalized schema
transform_data = PythonOperator(
    task_id='transform_strackr_data',
    python_callable=transform_strackr_data_task,
    dag=dag,
    doc_md="""
    ## Transform Strackr Data
    
    This task transforms raw Strackr transaction inquiries to our normalized schema.
    
    **What it does:**
    - Takes raw transaction data from previous task
    - Maps Strackr fields to normalized transaction schema
    - Handles data type conversions and validation
    - Filters out invalid transactions
    - Generates transformation summary statistics
    
    **Output:**
    - Normalized transaction count
    - Transformation summary
    - Processing time
    - Normalized transaction data (passed to next task)
    """,
)

# Task 3: Validate data quality and store results
validate_and_store = PythonOperator(
    task_id='validate_and_store_strackr_data',
    python_callable=validate_and_store_strackr_data_task,
    dag=dag,
    doc_md="""
    ## Validate and Store Strackr Data
    
    This task validates the normalized data quality and stores results in GCS.
    
    **What it does:**
    - Validates data quality metrics (completeness, consistency)
    - Checks for missing required fields
    - Calculates summary statistics
    - Stores normalized transactions in GCS
    - Generates final processing report
    
    **Output:**
    - Data quality validation results
    - GCS storage path
    - Final transaction count
    - Processing time
    """,
)

# Define task dependencies
fetch_transactions >> transform_data >> validate_and_store

# Add task documentation
dag.doc_md = """
# Strackr Transaction Reports DAG

This DAG processes transaction inquiries from Strackr's affiliate network platform.

## Overview

Strackr is an affiliate marketing platform that tracks transaction inquiries from various 
affiliate networks. This DAG fetches transaction data, normalizes it to our standard schema,
and stores it for downstream processing.

## Data Flow

1. **Fetch**: Get transaction inquiries from Strackr API for yesterday
2. **Transform**: Convert to normalized transaction schema
3. **Validate & Store**: Check data quality and store in GCS

## Schedule

- **Frequency**: Daily at 2 AM UTC
- **Catchup**: Disabled (only processes current data)
- **Retries**: 2 attempts with 5-minute delays

## Monitoring

- Email notifications <NAME_EMAIL> on failures
- Task logs available in Airflow UI
- Data quality metrics logged for each run

## Dependencies

- Strackr API credentials stored in Google Secret Manager
- GCS bucket for data storage
- Network connectivity to Strackr API endpoints

## Data Schema

The normalized transaction schema includes:
- Transaction identifiers and metadata
- Financial data (amounts, currency, commissions)
- Merchant and network information
- Status and timing information
- Platform-specific custom fields

## Troubleshooting

Common issues and solutions:
- **Authentication failures**: Check Strackr API credentials in Secret Manager
- **Network timeouts**: Verify connectivity to api.strackr.com
- **Data validation errors**: Check Strackr API response format changes
- **Storage failures**: Verify GCS bucket permissions and quotas
"""

if __name__ == "__main__":
    dag.test()
