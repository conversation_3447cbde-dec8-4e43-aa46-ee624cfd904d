#!/usr/bin/env python3
"""
Test script for Strackr integration.
Validates the complete pipeline works end-to-end before deploying to Airflow.

Usage:
    python test_strackr.py
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# Add the dags directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'dags'))

from dependencies.transaction_reports import (
    StrackrClient,
    StrackrAuth,
    transform_strackr_transactions,
    get_date_range_for_yesterday,
    validate_transaction_data_quality
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_authentication():
    """Test Strackr API authentication."""
    logger.info("Testing Strackr authentication...")
    
    try:
        auth = StrackrAuth()
        is_valid = auth.validate_credentials()
        
        if is_valid:
            logger.info("✅ Authentication test passed")
            return True
        else:
            logger.error("❌ Authentication test failed - invalid credentials")
            return False
            
    except Exception as e:
        logger.error(f"❌ Authentication test failed with error: {str(e)}")
        return False

def test_api_client():
    """Test Strackr API client functionality."""
    logger.info("Testing Strackr API client...")
    
    try:
        client = StrackrClient()
        
        # Test with a small date range (last 7 days)
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=7)
        
        start_str = start_date.strftime('%Y-%m-%dT00:00:00Z')
        end_str = end_date.strftime('%Y-%m-%dT23:59:59Z')
        
        logger.info(f"Fetching transactions from {start_str} to {end_str}")
        
        transactions = client.get_transaction_inquiries(
            start_date=start_str,
            end_date=end_str,
            limit=10  # Limit to 10 for testing
        )
        
        logger.info(f"✅ API client test passed - retrieved {len(transactions)} transactions")
        return True, transactions
        
    except Exception as e:
        logger.error(f"❌ API client test failed with error: {str(e)}")
        return False, []

def test_data_transformation(raw_transactions):
    """Test data transformation functionality."""
    logger.info("Testing data transformation...")
    
    try:
        if not raw_transactions:
            logger.warning("⚠️  No raw transactions to transform - skipping transformation test")
            return True, []
        
        normalized_transactions = transform_strackr_transactions(raw_transactions)
        
        logger.info(f"✅ Transformation test passed - normalized {len(normalized_transactions)} transactions")
        
        # Show sample normalized transaction
        if normalized_transactions:
            sample = normalized_transactions[0]
            logger.info(f"Sample normalized transaction:")
            logger.info(f"  - ID: {sample.transaction_id}")
            logger.info(f"  - Platform: {sample.platform}")
            logger.info(f"  - Amount: {sample.order_amount} {sample.currency}")
            logger.info(f"  - Commission: {sample.commission_amount} {sample.currency}")
            logger.info(f"  - Status: {sample.status}")
            logger.info(f"  - Merchant: {sample.merchant_name}")
        
        return True, normalized_transactions
        
    except Exception as e:
        logger.error(f"❌ Transformation test failed with error: {str(e)}")
        return False, []

def test_data_validation(normalized_transactions):
    """Test data validation functionality."""
    logger.info("Testing data validation...")
    
    try:
        if not normalized_transactions:
            logger.warning("⚠️  No normalized transactions to validate - skipping validation test")
            return True
        
        validation_results = validate_transaction_data_quality(
            normalized_transactions,
            platform='strackr'
        )
        
        logger.info(f"✅ Validation test passed")
        logger.info(f"Validation results:")
        logger.info(f"  - Valid: {validation_results['is_valid']}")
        logger.info(f"  - Transaction count: {validation_results['transaction_count']}")
        logger.info(f"  - Errors: {len(validation_results['errors'])}")
        logger.info(f"  - Warnings: {len(validation_results['warnings'])}")
        
        if validation_results['errors']:
            logger.warning(f"Validation errors: {validation_results['errors']}")
        
        if validation_results['warnings']:
            logger.warning(f"Validation warnings: {validation_results['warnings']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Validation test failed with error: {str(e)}")
        return False

def test_date_utilities():
    """Test date utility functions."""
    logger.info("Testing date utilities...")
    
    try:
        start_date, end_date = get_date_range_for_yesterday()
        logger.info(f"Yesterday's date range: {start_date} to {end_date}")
        
        # Validate format
        datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        
        logger.info("✅ Date utilities test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Date utilities test failed with error: {str(e)}")
        return False

def run_full_test():
    """Run the complete test suite."""
    logger.info("🚀 Starting Strackr integration test suite...")
    logger.info("=" * 60)
    
    test_results = []
    
    # Test 1: Authentication
    test_results.append(test_authentication())
    
    # Test 2: Date utilities
    test_results.append(test_date_utilities())
    
    # Test 3: API client
    api_success, raw_transactions = test_api_client()
    test_results.append(api_success)
    
    # Test 4: Data transformation
    transform_success, normalized_transactions = test_data_transformation(raw_transactions)
    test_results.append(transform_success)
    
    # Test 5: Data validation
    test_results.append(test_data_validation(normalized_transactions))
    
    # Summary
    logger.info("=" * 60)
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    if passed_tests == total_tests:
        logger.info(f"🎉 ALL TESTS PASSED ({passed_tests}/{total_tests})")
        logger.info("✅ Strackr integration is ready for deployment!")
        return True
    else:
        logger.error(f"❌ SOME TESTS FAILED ({passed_tests}/{total_tests})")
        logger.error("🚨 Fix the issues before deploying to Airflow")
        return False

if __name__ == "__main__":
    success = run_full_test()
    sys.exit(0 if success else 1)
