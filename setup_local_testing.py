#!/usr/bin/env python3
"""
Setup script for local Strackr testing.
Creates .env file and installs required dependencies.
"""

import os
import subprocess
import sys

def install_dependencies():
    """Install required Python packages."""
    print("📦 Installing required dependencies...")
    
    required_packages = [
        'python-dotenv',
        'requests',
        'google-cloud-storage',  # For future GCS testing
    ]
    
    for package in required_packages:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")
            return False
    
    return True

def create_env_file():
    """Create .env file from template if it doesn't exist."""
    env_file = '.env'
    example_file = '.env.example'
    
    if os.path.exists(env_file):
        print(f"✅ {env_file} already exists")
        return True
    
    if not os.path.exists(example_file):
        print(f"❌ {example_file} not found")
        return False
    
    try:
        # Copy example to .env
        with open(example_file, 'r') as src:
            content = src.read()
        
        with open(env_file, 'w') as dst:
            dst.write(content)
        
        print(f"✅ Created {env_file} from {example_file}")
        print(f"📝 Please edit {env_file} and add your Strackr API credentials")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create {env_file}: {str(e)}")
        return False

def get_credentials_interactively():
    """Prompt user for credentials and update .env file."""
    env_file = '.env'
    
    print("\n" + "="*50)
    print("STRACKR API CREDENTIALS SETUP")
    print("="*50)
    
    print("Please enter your Strackr API credentials:")
    print("(You can find these in your Strackr account settings)")
    
    api_id = input("\nStrackr API ID: ").strip()
    if not api_id:
        print("❌ API ID is required")
        return False
    
    api_key = input("Strackr API Key: ").strip()
    if not api_key:
        print("❌ API Key is required")
        return False
    
    try:
        # Read existing .env content
        env_content = []
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                env_content = f.readlines()
        
        # Update or add credentials
        updated_content = []
        api_id_set = False
        api_key_set = False
        
        for line in env_content:
            if line.startswith('STRACKR_API_ID='):
                updated_content.append(f'STRACKR_API_ID={api_id}\n')
                api_id_set = True
            elif line.startswith('STRACKR_API_KEY='):
                updated_content.append(f'STRACKR_API_KEY={api_key}\n')
                api_key_set = True
            else:
                updated_content.append(line)
        
        # Add missing credentials
        if not api_id_set:
            updated_content.append(f'STRACKR_API_ID={api_id}\n')
        if not api_key_set:
            updated_content.append(f'STRACKR_API_KEY={api_key}\n')
        
        # Add local testing flag if not present
        if not any(line.startswith('LOCAL_TESTING=') for line in updated_content):
            updated_content.append('LOCAL_TESTING=true\n')
        
        # Write updated content
        with open(env_file, 'w') as f:
            f.writelines(updated_content)
        
        print(f"✅ Credentials saved to {env_file}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to save credentials: {str(e)}")
        return False

def run_test():
    """Run the local test script."""
    print("\n" + "="*50)
    print("RUNNING LOCAL TESTS")
    print("="*50)
    
    try:
        result = subprocess.run([sys.executable, 'test_strackr_local.py'], 
                              capture_output=False, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Failed to run tests: {str(e)}")
        return False

def main():
    """Main setup function."""
    print("🚀 Strackr Local Testing Setup")
    print("=" * 40)
    
    # Step 1: Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        return False
    
    # Step 2: Create .env file
    if not create_env_file():
        print("❌ Failed to create .env file")
        return False
    
    # Step 3: Get credentials
    print("\n📝 Now we need to set up your Strackr API credentials...")
    choice = input("Do you want to enter them now? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes']:
        if not get_credentials_interactively():
            print("❌ Failed to set up credentials")
            print("📝 Please manually edit .env file and add your credentials")
            return False
        
        # Step 4: Run tests
        print("\n🧪 Ready to run tests!")
        choice = input("Run tests now? (y/n): ").lower().strip()
        
        if choice in ['y', 'yes']:
            return run_test()
    else:
        print("\n📝 Please edit .env file and add your Strackr API credentials")
        print("Then run: python test_strackr_local.py")
    
    print("\n✅ Setup complete!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
