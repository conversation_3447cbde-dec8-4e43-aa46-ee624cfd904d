"""
Normalized transaction schema for both Strackr and ShopMy data.
Provides unified data structure and validation functions.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class NormalizedTransaction:
    """
    Normalized transaction data structure that works for both Strackr and ShopMy.
    """
    # Universal Identifiers
    transaction_id: str              # Unique across platforms (platform_originalid)
    platform: str                   # 'strackr' or 'shopmy'
    source_transaction_id: str       # Original platform transaction ID
    
    # Financial Data
    currency: str                    # Currency code (USD, EUR, etc.)
    order_amount: float              # Order/purchase amount
    commission_amount: float         # Commission/revenue amount
    final_order_amount: Optional[float] = None      # Final confirmed order amount
    final_commission_amount: Optional[float] = None # Final confirmed commission
    
    # Transaction Details
    order_id: str                    # Order ID from merchant/advertiser
    customer_id: Optional[str] = None # Customer identifier
    transaction_date: datetime       # When the sale/transaction occurred
    created_date: datetime           # When the record was created in platform
    
    # Merchant/Network Information
    network_name: str                # Affiliate network name
    merchant_name: str               # Merchant/advertiser name
    merchant_id: Optional[str] = None # Merchant/advertiser ID
    connection_name: Optional[str] = None # Connection/account name
    
    # Status & Classification
    status: str                      # pending, confirmed, declined, etc.
    transaction_type: Optional[str] = None # inquiry, sale, etc.
    decline_reason: Optional[str] = None   # Reason if declined
    
    # Additional Metadata
    channel_name: Optional[str] = None     # Traffic channel/source
    custom_fields: Optional[Dict] = None   # Platform-specific data
    comments: Optional[str] = None         # Any comments/notes
    last_updated: datetime                 # When record was last updated
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for BigQuery/storage."""
        result = {}
        for field_name, field_value in self.__dict__.items():
            if isinstance(field_value, datetime):
                result[field_name] = field_value.isoformat()
            elif field_value is not None:
                result[field_name] = field_value
            else:
                result[field_name] = None
        return result

# BigQuery Schema Definition
BIGQUERY_SCHEMA = [
    {"name": "transaction_id", "type": "STRING", "mode": "REQUIRED"},
    {"name": "platform", "type": "STRING", "mode": "REQUIRED"},
    {"name": "source_transaction_id", "type": "STRING", "mode": "REQUIRED"},
    
    {"name": "currency", "type": "STRING", "mode": "REQUIRED"},
    {"name": "order_amount", "type": "FLOAT", "mode": "REQUIRED"},
    {"name": "commission_amount", "type": "FLOAT", "mode": "REQUIRED"},
    {"name": "final_order_amount", "type": "FLOAT", "mode": "NULLABLE"},
    {"name": "final_commission_amount", "type": "FLOAT", "mode": "NULLABLE"},
    
    {"name": "order_id", "type": "STRING", "mode": "REQUIRED"},
    {"name": "customer_id", "type": "STRING", "mode": "NULLABLE"},
    {"name": "transaction_date", "type": "TIMESTAMP", "mode": "REQUIRED"},
    {"name": "created_date", "type": "TIMESTAMP", "mode": "REQUIRED"},
    
    {"name": "network_name", "type": "STRING", "mode": "REQUIRED"},
    {"name": "merchant_name", "type": "STRING", "mode": "REQUIRED"},
    {"name": "merchant_id", "type": "STRING", "mode": "NULLABLE"},
    {"name": "connection_name", "type": "STRING", "mode": "NULLABLE"},
    
    {"name": "status", "type": "STRING", "mode": "REQUIRED"},
    {"name": "transaction_type", "type": "STRING", "mode": "NULLABLE"},
    {"name": "decline_reason", "type": "STRING", "mode": "NULLABLE"},
    
    {"name": "channel_name", "type": "STRING", "mode": "NULLABLE"},
    {"name": "custom_fields", "type": "STRING", "mode": "NULLABLE"},  # JSON string
    {"name": "comments", "type": "STRING", "mode": "NULLABLE"},
    {"name": "last_updated", "type": "TIMESTAMP", "mode": "REQUIRED"},
]

def validate_transaction(transaction: NormalizedTransaction) -> List[str]:
    """
    Validate a normalized transaction and return list of validation errors.
    
    Args:
        transaction: NormalizedTransaction instance to validate
        
    Returns:
        List of validation error messages (empty if valid)
    """
    errors = []
    
    # Required field validation
    if not transaction.transaction_id:
        errors.append("transaction_id is required")
    
    if not transaction.platform:
        errors.append("platform is required")
    elif transaction.platform not in ['strackr', 'shopmy']:
        errors.append(f"platform must be 'strackr' or 'shopmy', got '{transaction.platform}'")
    
    if not transaction.source_transaction_id:
        errors.append("source_transaction_id is required")
    
    if not transaction.currency:
        errors.append("currency is required")
    elif len(transaction.currency) != 3:
        errors.append("currency must be 3-character ISO code")
    
    if transaction.order_amount is None or transaction.order_amount < 0:
        errors.append("order_amount must be non-negative number")
    
    if transaction.commission_amount is None or transaction.commission_amount < 0:
        errors.append("commission_amount must be non-negative number")
    
    if not transaction.order_id:
        errors.append("order_id is required")
    
    if not transaction.transaction_date:
        errors.append("transaction_date is required")
    
    if not transaction.created_date:
        errors.append("created_date is required")
    
    if not transaction.network_name:
        errors.append("network_name is required")
    
    if not transaction.merchant_name:
        errors.append("merchant_name is required")
    
    if not transaction.status:
        errors.append("status is required")
    
    if not transaction.last_updated:
        errors.append("last_updated is required")
    
    return errors

def is_valid_transaction(transaction: NormalizedTransaction) -> bool:
    """
    Check if a transaction is valid.
    
    Args:
        transaction: NormalizedTransaction instance to validate
        
    Returns:
        True if valid, False otherwise
    """
    return len(validate_transaction(transaction)) == 0
