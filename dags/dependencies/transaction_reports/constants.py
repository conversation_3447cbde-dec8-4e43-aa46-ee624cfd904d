"""
Constants for transaction reports processing.
Contains API endpoints, default parameters, and configuration values.
"""

# Project Configuration
PROJECT_ID = "phia-prod-416420"
DATASET_ID = "transaction_reports"

# Strackr API Configuration
STRACKR_BASE_URL = "https://api.strackr.com/v4"
STRACKR_TRANSACTION_INQUIRIES_ENDPOINT = "/im/reports/transaction_inquiries"

# API Request Defaults
DEFAULT_PAGE_SIZE = 100  # Max allowed by Strackr API
MAX_RETRIES = 3
RETRY_DELAY = 5  # seconds
REQUEST_TIMEOUT = 30  # seconds

# Date/Time Configuration
DEFAULT_TIME_TYPE = "sold_at"  # Options: sold_at, created_at, status_updated_at
DEFAULT_CURRENCY = "USD"

# Strackr API Parameters
STRACKR_EXPAND_FIELDS = [
    "reason",
    "network_favicon", 
    "advertiser_favicon"
]

STRACKR_STATUS_TYPES = [
    "pending",
    "confirmed", 
    "declined"
]

STRACKR_TRANSACTION_TYPES = [
    "unknown",
    "untracked",
    "incorrect", 
    "declined"
]

# GCS Configuration
GCS_BUCKET_NAME = "transaction-reports-data"
STRACKR_RAW_DATA_PREFIX = "strackr/raw"
STRACKR_PROCESSED_DATA_PREFIX = "strackr/processed"
SHOPMY_RAW_DATA_PREFIX = "shopmy/raw"
SHOPMY_PROCESSED_DATA_PREFIX = "shopmy/processed"

# BigQuery Configuration
STRACKR_TABLE_ID = f"{PROJECT_ID}.{DATASET_ID}.strackr_transactions"
SHOPMY_TABLE_ID = f"{PROJECT_ID}.{DATASET_ID}.shopmy_transactions"
NORMALIZED_TABLE_ID = f"{PROJECT_ID}.{DATASET_ID}.normalized_transactions"

# Secret Manager Paths
STRACKR_API_ID_SECRET = f"projects/{PROJECT_ID}/secrets/strackr-api-id/versions/latest"
STRACKR_API_KEY_SECRET = f"projects/{PROJECT_ID}/secrets/strackr-api-key/versions/latest"

# Platform Identifiers
PLATFORM_STRACKR = "strackr"
PLATFORM_SHOPMY = "shopmy"

# Data Quality Thresholds
MIN_EXPECTED_TRANSACTIONS_PER_DAY = 10
MAX_EXPECTED_TRANSACTIONS_PER_DAY = 10000

# Logging Configuration
LOG_LEVEL = "INFO"
