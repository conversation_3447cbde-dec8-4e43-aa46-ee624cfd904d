#!/usr/bin/env python3
"""
Simple standalone test for Strackr API without any Google Cloud dependencies.
Tests the API directly using environment variables.
"""

import os
import sys
import requests
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any

# Load environment variables from .env file
try:
    from dotenv import load_dotenv

    load_dotenv()
    print("✅ Loaded environment variables from .env file")
except ImportError:
    print("⚠️  python-dotenv not installed. Install with: pip install python-dotenv")

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Constants
STRACKR_BASE_URL = "https://api.strackr.com/v4"
STRACKR_ENDPOINT = "/im/reports/transaction_inquiries"
DEFAULT_PAGE_SIZE = 10  # Small for testing
REQUEST_TIMEOUT = 30


def check_environment():
    """Check if required environment variables are set."""
    print("\n" + "=" * 50)
    print("CHECKING ENVIRONMENT")
    print("=" * 50)

    api_id = os.getenv("STRACKR_API_ID")
    api_key = os.getenv("STRACKR_API_KEY")

    if api_id:
        print(f"✅ STRACKR_API_ID: {'*' * len(api_id)}")
    else:
        print("❌ STRACKR_API_ID: Not set")
        return False, None, None

    if api_key:
        print(f"✅ STRACKR_API_KEY: {'*' * len(api_key)}")
    else:
        print("❌ STRACKR_API_KEY: Not set")
        return False, None, None

    print("✅ All required environment variables are set")
    return True, api_id, api_key


def test_strackr_api_direct(api_id: str, api_key: str):
    """Test Strackr API directly without any custom modules."""
    print("\n" + "=" * 50)
    print("TESTING STRACKR API DIRECTLY")
    print("=" * 50)

    try:
        # Build URL and parameters
        url = f"{STRACKR_BASE_URL}{STRACKR_ENDPOINT}"

        # Get date range for last 7 days
        from datetime import timezone

        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=7)

        start_str = start_date.strftime("%Y-%m-%dT00:00:00Z")
        end_str = end_date.strftime("%Y-%m-%dT23:59:59Z")

        print(f"📅 Date range: {start_str} to {end_str}")
        print(f"🌐 URL: {url}")

        # Build parameters
        params = {
            "api_id": api_id,
            "api_key": api_key,
            "time_start": start_str,
            "time_end": end_str,
            "time_type": "sold_at",
            "limit": DEFAULT_PAGE_SIZE,
            "page": 1,
            "expand": ["reason", "network_favicon", "advertiser_favicon"],
        }

        print(f"📋 Making API request...")

        # Make the request
        response = requests.get(url, params=params, timeout=REQUEST_TIMEOUT)

        print(f"📊 Response status: {response.status_code}")

        if response.status_code == 401:
            print("❌ Authentication failed - check your API credentials")
            return False, []
        elif response.status_code == 429:
            print("❌ Rate limit exceeded - try again later")
            return False, []
        elif response.status_code != 200:
            print(f"❌ API request failed with status {response.status_code}")
            print(f"Response: {response.text[:500]}")
            return False, []

        # Parse response
        data = response.json()
        transactions = data.get("data", [])

        print(f"✅ API request successful!")
        print(f"📄 Retrieved {len(transactions)} transactions")

        if transactions:
            sample = transactions[0]
            print(f"\n📄 Sample transaction:")
            print(f"   - ID: {sample.get('id', 'N/A')}")
            print(f"   - Order ID: {sample.get('order_id', 'N/A')}")
            print(f"   - Currency: {sample.get('currency', 'N/A')}")
            print(f"   - Order Amount: {sample.get('expected_order_amount', 'N/A')}")
            print(f"   - Commission: {sample.get('expected_revenue', 'N/A')}")
            print(f"   - Status: {sample.get('status_id', 'N/A')}")
            print(f"   - Network: {sample.get('network_name', 'N/A')}")
            print(f"   - Advertiser: {sample.get('advertiser_name', 'N/A')}")
            print(f"   - Date: {sample.get('sold_at', 'N/A')}")

            print(f"\n📋 Available fields: {list(sample.keys())}")
        else:
            print("ℹ️  No transactions found in the date range")
            print("💡 This might be normal if there's no recent transaction data")

        return True, transactions

    except requests.exceptions.Timeout:
        print("❌ Request timed out - check your internet connection")
        return False, []
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - check your internet connection")
        return False, []
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {str(e)}")
        return False, []
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False, []


def test_data_structure(transactions: List[Dict[str, Any]]):
    """Test the structure of returned data."""
    print("\n" + "=" * 50)
    print("TESTING DATA STRUCTURE")
    print("=" * 50)

    if not transactions:
        print("⚠️  No transactions to analyze")
        return True

    try:
        print(f"🔍 Analyzing {len(transactions)} transactions...")

        # Check required fields
        required_fields = [
            "id",
            "order_id",
            "currency",
            "expected_order_amount",
            "expected_revenue",
            "status_id",
            "network_name",
            "sold_at",
        ]

        missing_fields = []
        for field in required_fields:
            if not any(field in t for t in transactions):
                missing_fields.append(field)

        if missing_fields:
            print(f"❌ Missing required fields: {missing_fields}")
            return False

        print("✅ All required fields present")

        # Analyze data types and values
        sample = transactions[0]

        print(f"📊 Data analysis:")
        print(f"   - Transaction ID type: {type(sample.get('id'))}")
        print(f"   - Order amount type: {type(sample.get('expected_order_amount'))}")
        print(f"   - Commission type: {type(sample.get('expected_revenue'))}")
        print(f"   - Currency type: {type(sample.get('currency'))}")

        # Count statuses
        status_counts = {}
        currency_counts = {}
        total_commission = 0

        for transaction in transactions:
            status = transaction.get("status_id", "unknown")
            status_counts[status] = status_counts.get(status, 0) + 1

            currency = transaction.get("currency", "unknown")
            currency_counts[currency] = currency_counts.get(currency, 0) + 1

            commission = transaction.get("expected_revenue", 0)
            if isinstance(commission, (int, float)):
                total_commission += commission

        print(f"📈 Summary statistics:")
        print(f"   - Status distribution: {status_counts}")
        print(f"   - Currency distribution: {currency_counts}")
        print(f"   - Total commission: {total_commission:.2f}")

        return True

    except Exception as e:
        print(f"❌ Data structure test failed: {str(e)}")
        return False


def run_simple_test():
    """Run the simple standalone test."""
    print("🚀 Starting Simple Strackr API Test")
    print("This tests the API directly without any custom modules")
    print("=" * 60)

    # Check environment
    env_ok, api_id, api_key = check_environment()
    if not env_ok:
        print("\n❌ Environment check failed")
        print("📝 Please create a .env file with your Strackr API credentials:")
        print("STRACKR_API_ID=your_api_id_here")
        print("STRACKR_API_KEY=your_api_key_here")
        return False

    # Test API
    api_ok, transactions = test_strackr_api_direct(api_id, api_key)
    if not api_ok:
        print("\n❌ API test failed")
        return False

    # Test data structure
    data_ok = test_data_structure(transactions)
    if not data_ok:
        print("\n❌ Data structure test failed")
        return False

    print("\n" + "=" * 60)
    print("🎉 ALL TESTS PASSED!")
    print("✅ Strackr API is working correctly")
    print("✅ Your credentials are valid")
    print("✅ Data structure is as expected")
    print("🚀 Ready to proceed with full integration!")
    print("=" * 60)

    return True


if __name__ == "__main__":
    success = run_simple_test()
    sys.exit(0 if success else 1)
