"""
Local development version of Strackr API authentication.
Uses environment variables instead of Google Secret Manager for easier local testing.
"""

import os
import logging
from typing import Optional
from .constants import STRACKR_BASE_URL

logger = logging.getLogger(__name__)

class StrackrAuthLocal:
    """
    Local development version of Strackr authentication.
    Uses environment variables instead of Secret Manager.
    """
    
    def __init__(self):
        """Initialize authentication with credentials from environment variables."""
        self.base_url = os.getenv('STRACKR_BASE_URL', STRACKR_BASE_URL)
        self._api_id = None
        self._api_key = None
        
        # Load credentials immediately to fail fast if missing
        self._load_credentials()
        
    def _load_credentials(self):
        """Load credentials from environment variables."""
        self._api_id = os.getenv('STRACKR_API_ID')
        self._api_key = os.getenv('STRACKR_API_KEY')
        
        if not self._api_id:
            raise ValueError(
                "STRACKR_API_ID environment variable is required. "
                "Please set it in your .env file or environment."
            )
        
        if not self._api_key:
            raise ValueError(
                "STRACKR_API_KEY environment variable is required. "
                "Please set it in your .env file or environment."
            )
        
        logger.info("Successfully loaded Strackr credentials from environment variables")
    
    @property
    def api_id(self) -> str:
        """Get API ID from environment."""
        return self._api_id
    
    @property
    def api_key(self) -> str:
        """Get API Key from environment."""
        return self._api_key
    
    def get_auth_params(self) -> dict:
        """
        Get authentication parameters for Strackr API requests.
        
        Returns:
            Dictionary with api_id and api_key for query parameters
        """
        return {
            'api_id': self.api_id,
            'api_key': self.api_key
        }
    
    def get_authenticated_url(self, endpoint: str) -> str:
        """
        Get full URL with authentication parameters.
        
        Args:
            endpoint: API endpoint (e.g., '/im/reports/transaction_inquiries')
            
        Returns:
            Full URL with base URL and endpoint
        """
        return f"{self.base_url}{endpoint}"
    
    def validate_credentials(self) -> bool:
        """
        Validate that credentials are available and properly formatted.
        
        Returns:
            True if credentials are valid, False otherwise
        """
        try:
            if not self.api_id or not self.api_key:
                logger.error("API ID or API Key is empty")
                return False
            
            if len(self.api_id.strip()) == 0 or len(self.api_key.strip()) == 0:
                logger.error("API ID or API Key contains only whitespace")
                return False
            
            logger.info("Strackr API credentials validated successfully")
            return True
            
        except Exception as e:
            logger.error(f"Credential validation failed: {str(e)}")
            return False
