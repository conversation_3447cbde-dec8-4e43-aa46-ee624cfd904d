#!/usr/bin/env python3
"""
Manual testing script for Strackr integration.
Test individual components step by step.
"""

import sys
import os
import logging
from datetime import datetime, timedel<PERSON>

# Add the dags directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'dags'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_step_1_authentication():
    """Test 1: Authentication"""
    print("\n" + "="*50)
    print("STEP 1: Testing Authentication")
    print("="*50)
    
    try:
        from dependencies.transaction_reports.strackr_auth import StrackrAuth
        
        auth = StrackrAuth()
        print(f"✅ StrackrAuth initialized successfully")
        
        # Test credential validation
        is_valid = auth.validate_credentials()
        if is_valid:
            print(f"✅ Credentials are valid")
            
            # Show auth params (without exposing actual values)
            auth_params = auth.get_auth_params()
            print(f"✅ Auth params structure: {list(auth_params.keys())}")
            
            return True
        else:
            print(f"❌ Credentials are invalid")
            return False
            
    except Exception as e:
        print(f"❌ Authentication test failed: {str(e)}")
        return False

def test_step_2_api_client():
    """Test 2: API Client"""
    print("\n" + "="*50)
    print("STEP 2: Testing API Client")
    print("="*50)
    
    try:
        from dependencies.transaction_reports.strackr_client import StrackrClient
        
        client = StrackrClient()
        print(f"✅ StrackrClient initialized successfully")
        
        # Test with a very recent small date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=3)  # Last 3 days
        
        start_str = start_date.strftime('%Y-%m-%dT00:00:00Z')
        end_str = end_date.strftime('%Y-%m-%dT23:59:59Z')
        
        print(f"📅 Testing date range: {start_str} to {end_str}")
        
        # Fetch with small limit for testing
        transactions = client.get_transaction_inquiries(
            start_date=start_str,
            end_date=end_str,
            limit=5  # Small limit for testing
        )
        
        print(f"✅ API call successful - retrieved {len(transactions)} transactions")
        
        if transactions:
            sample = transactions[0]
            print(f"📄 Sample transaction fields: {list(sample.keys())[:10]}...")
            print(f"📄 Sample transaction ID: {sample.get('id', 'N/A')}")
            print(f"📄 Sample order amount: {sample.get('expected_order_amount', 'N/A')}")
        
        return True, transactions
        
    except Exception as e:
        print(f"❌ API client test failed: {str(e)}")
        return False, []

def test_step_3_transformation(raw_transactions):
    """Test 3: Data Transformation"""
    print("\n" + "="*50)
    print("STEP 3: Testing Data Transformation")
    print("="*50)
    
    try:
        from dependencies.transaction_reports.strackr_transform import transform_strackr_transactions
        
        if not raw_transactions:
            print("⚠️  No raw transactions to transform")
            return True, []
        
        print(f"🔄 Transforming {len(raw_transactions)} raw transactions...")
        
        normalized = transform_strackr_transactions(raw_transactions)
        
        print(f"✅ Transformation successful - {len(normalized)} normalized transactions")
        
        if normalized:
            sample = normalized[0]
            print(f"📄 Sample normalized transaction:")
            print(f"   - ID: {sample.transaction_id}")
            print(f"   - Platform: {sample.platform}")
            print(f"   - Currency: {sample.currency}")
            print(f"   - Order Amount: {sample.order_amount}")
            print(f"   - Commission: {sample.commission_amount}")
            print(f"   - Status: {sample.status}")
            print(f"   - Merchant: {sample.merchant_name}")
        
        return True, normalized
        
    except Exception as e:
        print(f"❌ Transformation test failed: {str(e)}")
        return False, []

def test_step_4_validation(normalized_transactions):
    """Test 4: Data Validation"""
    print("\n" + "="*50)
    print("STEP 4: Testing Data Validation")
    print("="*50)
    
    try:
        from dependencies.transaction_reports.utils import validate_transaction_data_quality
        
        if not normalized_transactions:
            print("⚠️  No normalized transactions to validate")
            return True
        
        print(f"🔍 Validating {len(normalized_transactions)} transactions...")
        
        validation_results = validate_transaction_data_quality(
            normalized_transactions,
            platform='strackr'
        )
        
        print(f"✅ Validation complete")
        print(f"📊 Results:")
        print(f"   - Valid: {validation_results['is_valid']}")
        print(f"   - Transaction count: {validation_results['transaction_count']}")
        print(f"   - Errors: {len(validation_results['errors'])}")
        print(f"   - Warnings: {len(validation_results['warnings'])}")
        
        if validation_results['errors']:
            print(f"❌ Errors found: {validation_results['errors']}")
        
        if validation_results['warnings']:
            print(f"⚠️  Warnings: {validation_results['warnings']}")
        
        # Show metrics
        metrics = validation_results.get('metrics', {})
        if metrics:
            print(f"📈 Metrics:")
            print(f"   - Total commission: {metrics.get('total_commission_amount', 0):.2f}")
            print(f"   - Total order value: {metrics.get('total_order_value', 0):.2f}")
            print(f"   - Average commission: {metrics.get('average_commission', 0):.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation test failed: {str(e)}")
        return False

def test_step_5_main_function():
    """Test 5: Main Function"""
    print("\n" + "="*50)
    print("STEP 5: Testing Main Function")
    print("="*50)
    
    try:
        from dependencies.transaction_reports.strackr_csv import get_normalized_strackr_transactions
        
        # Test with last 2 days
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=2)
        
        start_str = start_date.strftime('%Y-%m-%dT00:00:00Z')
        end_str = end_date.strftime('%Y-%m-%dT23:59:59Z')
        
        print(f"🎯 Testing main function with date range: {start_str} to {end_str}")
        
        normalized_transactions = get_normalized_strackr_transactions(start_str, end_str)
        
        print(f"✅ Main function successful - {len(normalized_transactions)} transactions")
        
        return True
        
    except Exception as e:
        print(f"❌ Main function test failed: {str(e)}")
        return False

def run_manual_tests():
    """Run manual tests step by step"""
    print("🚀 Starting Manual Strackr Tests")
    print("This will test each component individually...")
    
    # Step 1: Authentication
    auth_success = test_step_1_authentication()
    if not auth_success:
        print("\n❌ Authentication failed - stopping tests")
        return False
    
    # Step 2: API Client
    api_success, raw_transactions = test_step_2_api_client()
    if not api_success:
        print("\n❌ API client failed - stopping tests")
        return False
    
    # Step 3: Transformation
    transform_success, normalized_transactions = test_step_3_transformation(raw_transactions)
    if not transform_success:
        print("\n❌ Transformation failed - stopping tests")
        return False
    
    # Step 4: Validation
    validation_success = test_step_4_validation(normalized_transactions)
    if not validation_success:
        print("\n❌ Validation failed - stopping tests")
        return False
    
    # Step 5: Main Function
    main_success = test_step_5_main_function()
    if not main_success:
        print("\n❌ Main function failed")
        return False
    
    print("\n" + "="*50)
    print("🎉 ALL MANUAL TESTS PASSED!")
    print("✅ Strackr integration is working correctly")
    print("="*50)
    
    return True

if __name__ == "__main__":
    success = run_manual_tests()
    sys.exit(0 if success else 1)
