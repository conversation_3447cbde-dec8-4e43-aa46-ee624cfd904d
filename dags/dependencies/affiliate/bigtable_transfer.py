import logging
from datetime import datetime, UTC
from google.cloud import bigquery
from airflow.providers.google.cloud.hooks.bigtable import BigtableHook
import pandas as pd
from google.cloud.bigtable import Client
from google.cloud.bigtable.batcher import MutationsBatcher

logger = logging.getLogger(__name__)


def transfer_to_bigtable(project_id: str, bigquery_table_id: str, bigtable_instance: str, bigtable_table: str, **kwargs):
    """Transfer data from BigQuery to Bigtable.

    Args:
        project_id: GCP project ID
        bigquery_table_id: Full BigQuery table ID (project.dataset.table)
        bigtable_instance: Bigtable instance ID
        bigtable_table: Bigtable table ID
    """
    logger.info(f"Starting transfer to Bigtable from {bigquery_table_id} to {bigtable_instance}.{bigtable_table}")

    try:
        bq_client = bigquery.Client()

        query = f"""
        SELECT *
        FROM `{bigquery_table_id}`
        """

        logger.info("Executing BigQuery query")
        df = bq_client.query(query).to_dataframe()
        logger.info(f"Retrieved {len(df)} rows from BigQuery table {bigquery_table_id}")

        logger.info("Processing and filtering data")
        df['networkId'] = df['networkName'].str.lower().str.replace(" ", "_")
        df = df.sort_values('commissionRateMax', ascending=False).drop_duplicates(['domain', 'networkId'])
        logger.info(f"Filtered to {len(df)} unique domain+network combinations after deduplication")

        logger.info(f"Connecting to Bigtable instance {bigtable_instance}")
        bt_hook = BigtableHook()
        instance = bt_hook.get_instance(project_id=project_id, instance_id=bigtable_instance)
        table = instance.table(bigtable_table)

        logger.info("Clearing existing rows with merchant_link# prefix")
        prefix = "merchant_link#".encode()
        table.drop_by_prefix(row_key_prefix=prefix)
        logger.info("Successfully cleared existing rows")

        timestamp = datetime.now(UTC)
        batch_size = 1000
        total_rows = len(df)
        total_batches = (total_rows + batch_size - 1) // batch_size  # Ceiling division

        logger.info(f"Starting batch processing with batch size {batch_size}")

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min((batch_idx + 1) * batch_size, total_rows)
            current_batch = df.iloc[start_idx:end_idx]

            logger.info(f"Processing batch {batch_idx + 1}/{total_batches} (rows {start_idx + 1}-{end_idx})")

            rows = []
            for _, row in current_batch.iterrows():
                row_key = f"merchant_link#{row['domain']}#{row['networkId']}".encode()
                direct_row = table.direct_row(row_key)

                for column in df.columns:
                    if pd.notna(row[column]):
                        value = str(row[column]).encode()
                        direct_row.set_cell(
                            'values',  # Column family
                            column.encode(),  # Column qualifier
                            value,  # Value
                            timestamp=timestamp
                        )

                rows.append(direct_row)

            logger.info(f"Starting batch mutation of {len(rows)} rows")
            with MutationsBatcher(table=table) as batcher:
                batcher.mutate_rows(rows)
            logger.info(f"Successfully transferred batch {batch_idx + 1}/{total_batches}")

        logger.info(f"Successfully completed transfer of {total_rows} rows to Bigtable table {  }")

    except Exception as e:
        logger.error(f"Error during BigQuery to Bigtable transfer: {str(e)}", exc_info=True)
        raise
