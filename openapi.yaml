openapi: 3.1.0
info:
  version: 4.0.0
  title: Strackr API
  description: |
    This API documentation is STRICTLY CONFIDENTIAL.

    CONFIDENTIALITY NOTICE: This document contains confidential and proprietary information.
    It must not be distributed, shared, or published on public platforms such as GitHub
    or any other code/documentation sharing service.
  contact:
    name: API Support
    url: https://strackr.com/contact
    email: <EMAIL>
  license:
    name: Private API
    url: https://strackr.com
  x-logo:
    url: https://strackr.com/assets/svg/logo.svg
    altText: Strackr.com
servers:
  - url: https://api.strackr.com/v4
    description: Main (production) server with live database
security:
  - apiIdQuery: []
    apiKeyQuery: []
tags:
  - name: Common Networks
    description: Networks
    x-displayName: Networks
  - name: Common Advertisers
    description: Advertisers
    x-displayName: Advertisers
  - name: Common Views
    description: Views
    x-displayName: Views
  - name: Common Categories
    description: Categories
    x-displayName: Categories
  - name: AD Reports
    description: Reports
    x-displayName: Reports
  - name: AD Statistics
    description: Statistics
    x-displayName: Statistics
  - name: AD Performances
    description: Performances
    x-displayName: Performances
  - name: AD Tools
    description: Tools
    x-displayName: Tools
  - name: IM Networks
    description: Networks
    x-displayName: Networks
  - name: IM Transaction inquiries
    description: Transaction inquiries
    x-displayName: Transaction inquiries
  - name: IM Inquiries
    description: Inquiries
    x-displayName: Inquiries
  - name: IM Uploads
    description: Uploads
    x-displayName: Uploads
  - name: IM Statistics
    description: Statistics
    x-displayName: Statistics
paths:
  /advertisers:
    get:
      summary: Advertisers
      operationId: listAdvertisers
      description: |
        List all advertisers for your account, an advertiser will be listed if there is any associated data, such as status, transactions, clicks, etc. 
        You can expand the data to display the programs associated with each advertiser. 
        There are two commission fields: one for global commissions for the advertiser and another for commissions per program.
        If you have tens or hundreds of thousands of advertisers, you will need to use pagination to avoid timeouts.
      tags:
        - Common Advertisers
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - logo
                - favicon
                - website
                - features
                - categories
                - programs
                - commissions
                - status_id
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/categories'
        - $ref: '#/components/parameters/countries'
        - $ref: '#/components/parameters/connections'
      responses:
        '200':
          description: An array of advertisers
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Advertiser'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - id: XXXXXXXX
                        name: Cdiscount FR
                        network_id: YYYY
                        network_name: Awin
                        logo: null
                        favicon: null
                        website: null
                        country: FR
                        source_id: '6948'
                        cookie_hours: null
                        status_id: joined
                        categories:
                          - id: XXXXXXXX
                            name: My category
                            color_id: 7
                        commissions:
                          ratio: null
                          fixed: null
                          cpc: null
                        programs:
                          - id: XXXXXXXX
                            source_id: null
                            connection_id: XXXXXXXX
                            connection_name: Awin FR
                            channel_id: null
                            channel_name: null
                            status_id: joined
                            status_updated_at: '2018-08-09T11:10:12Z'
                            commissions:
                              ratio:
                                min: 0
                                max: 8
                              fixed:
                                min: 0
                                max: 3
                              cpc: null
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /advertisers/{advertiserId}:
    get:
      summary: Advertiser
      operationId: getAdvertiser
      description: Advertiser information, you can expand the data to display the programs associated with this advertiser.
      tags:
        - Common Advertisers
      parameters:
        - $ref: '#/components/parameters/advertiserId'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - logo
                - favicon
                - website
                - features
                - categories
                - programs
                - commissions
                - status_id
      responses:
        '200':
          description: Advertiser data
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/EntityResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        $ref: '#/components/schemas/Advertiser'
              examples:
                '0':
                  value:
                    data:
                      id: XXXXXXXX
                      name: Cdiscount FR
                      network_id: YYYY
                      network_name: Awin
                      logo: null
                      favicon: null
                      website: null
                      country: FR
                      source_id: '6948'
                      cookie_hours: null
                      status_id: joined
                      categories:
                        - id: XXXXXXXX
                          name: My category
                          color_id: 7
                      commissions:
                        ratio: null
                        fixed: null
                        cpc: null
                      programs:
                        - id: XXXXXXXX
                          source_id: null
                          connection_id: XXXXXXXX
                          connection_name: Awin FR
                          channel_id: null
                          channel_name: null
                          status_id: joined
                          status_updated_at: '2018-08-09T11:10:12Z'
                          commissions:
                            ratio:
                              min: 0
                              max: 8
                            fixed:
                              min: 0
                              max: 3
                            cpc: null
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /advertisers/programs:
    get:
      summary: Programs
      operationId: listPrograms
      description: |
        List all programs associated with advertisers. 
        You can track the last status update with this endpoint by using the `sort_by` filter with the value `status_updated_at`.
      tags:
        - Common Advertisers
      parameters:
        - $ref: '#/components/parameters/timestartoptional'
        - $ref: '#/components/parameters/timeendoptional'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - advertiser_favicon
                - commissions
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/categories'
        - $ref: '#/components/parameters/countries'
        - $ref: '#/components/parameters/connections'
        - $ref: '#/components/parameters/channels'
        - $ref: '#/components/parameters/statusesprogram'
      responses:
        '200':
          description: An array of programs
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Program'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - id: XXXXXXXX
                        source_id: null
                        advertiser_id: XXXXXXXX
                        advertiser_source_id: '849260'
                        advertiser_name: Cdiscount FR
                        advertiser_favicon: null
                        country: FR
                        network_id: YYYY
                        network_name: Awin
                        connection_id: XXXXXXXX
                        connection_name: Awin FR
                        channel_id: null
                        channel_name: null
                        status_id: joined
                        status_updated_at: '2018-08-09T11:10:12Z'
                        commissions:
                          ratio:
                            min: 0
                            max: 8
                          fixed:
                            min: 0
                            max: 3
                          cpc: null
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /networks:
    get:
      summary: Networks
      operationId: listNetworks
      description: List all networks available for your account. You can expand the data to display the connections associated with each network.
      tags:
        - Common Networks
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - logo
                - favicon
                - connections
                - connections.data
                - connections.updates
                - connections.features
        - $ref: '#/components/parameters/views'
      responses:
        '200':
          description: An array of networks
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Network'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - id: XXXX
                        name: Awin
                        logo: null
                        favicon: null
                        connections:
                          - id: ZZZZZZZZ
                            name: Awin US
                            connector_id: XXXXXX
                            connector_name: API
                            state: pending
                            data:
                              is_advertisers: true
                              is_program_statuses: false
                              is_transactions: true
                              is_revenues: true
                              is_clicks: true
                              is_payments: false
                              is_deals: false
                              is_channels: true
                            features:
                              is_linkbuilder: true
                              is_inquiry_manager: false
                            updates:
                              updated_at: '2025-01-15T02:59:01'
                              updated_complete_at: '2025-01-15T02:59:01'
                              updated_statuses_at: '2025-01-15T00:23:42'
                              last_update_state: done
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /networks/{networkId}:
    get:
      summary: Network
      operationId: getNetwork
      description: Network information, you can expand the data to display the connections associated with this network.
      tags:
        - Common Networks
      parameters:
        - $ref: '#/components/parameters/networkId'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - logo
                - favicon
                - connections
                - connections.data
                - connections.updates
                - connections.features
      responses:
        '200':
          description: Network data
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/EntityResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        $ref: '#/components/schemas/Network'
              examples:
                '0':
                  value:
                    data:
                      id: XXXX
                      name: Awin
                      logo: null
                      favicon: null
                      connections:
                        - id: ZZZZZZZZ
                          name: Awin US
                          connector_id: XXXXXX
                          connector_name: API
                          state: pending
                          data:
                            is_advertisers: true
                            is_program_statuses: false
                            is_transactions: true
                            is_revenues: true
                            is_clicks: true
                            is_payments: false
                            is_deals: false
                            is_channels: true
                          features:
                            is_linkbuilder: true
                            is_inquiry_manager: false
                          updates:
                            updated_at: '2025-01-15T02:59:01'
                            updated_complete_at: '2025-01-15T02:59:01'
                            updated_statuses_at: '2025-01-15T00:23:42'
                            last_update_state: done
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /networks/channels:
    get:
      summary: Channels
      operationId: listChannels
      description: |
        List all channels available for your account. A channel is a source, such as a website, that you can create on networks. 
        If a network has channels, you can use the channel ID to pull data by channel.
      tags:
        - Common Networks
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/views'
      responses:
        '200':
          description: An array of channels
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Channel'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - id: ZZZZZZZZ
                        name: XXXXXXX
                        source_id: '249282'
                        network_id: ZZZZ
                        network_name: TradeTracker
                        connection_id: WWWWWWWW
                        connection_name: Tradetracker NL account
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /views:
    get:
      summary: Views
      operationId: listViews
      description: List all views created in the dashboard. You can use the view ID to filter data in several endpoints.
      tags:
        - Common Views
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
      responses:
        '200':
          description: An array of views
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/View'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - id: ZZZZZZZ
                        name: My view
                        connections:
                          - id: XXXXX
                            name: Awin Website A
                        channels:
                          - id: YYYYY
                            name: Channel A
                        categories:
                          - id: ZZZZZ
                            name: Category A
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /categories:
    get:
      summary: Categories
      operationId: listCategories
      description: List all your categories that group advertisers. You can use the category ID to filter data in several endpoints.
      tags:
        - Common Categories
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - advertisers
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/views'
      responses:
        '200':
          description: An array of categories
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Category'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - id: ZZZZZZZ
                        name: My category
                        color_id: 7
                        advertisers:
                          - id: XXXXXXXX
                            name: Expert.nl
                            network_id: XXXX
                            network_name: Awin
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
    post:
      summary: New category
      operationId: newCategory
      description: |
        Create a new category by providing a name and a list of advertiser IDs. 
        The `color_id` is used to define the color in the dashboard.
      tags:
        - Common Categories
      requestBody:
        description: The category to create.
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - advertisers
              properties:
                name:
                  type: string
                  description: Name of the category
                  example: My category
                  minLength: 2
                  maxLength: 45
                color_id:
                  type: integer
                  description: Color ID used in the dashboard
                  minimum: 1
                  maximum: 10
                advertisers:
                  type: array
                  description: List of advertiser IDs
                  items:
                    type: string
                    example: xxxxxxxx
                    minLength: 8
                    maxLength: 8
            examples:
              '0':
                value:
                  name: My new category
                  color_id: 7
                  advertisers:
                    - XXXXXXXX
                    - YYYYYYYY
      responses:
        '200':
          description: A category
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/EntityResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        $ref: '#/components/schemas/Category'
              examples:
                '0':
                  value:
                    data:
                      id: ZZZZZZZ
                      name: My category
                      color_id: 7
                      advertisers:
                        - id: XXXXXXXX
                          name: Expert.nl
                          network_id: XXXX
                          network_name: Awin
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /categories/{categoryId}:
    get:
      summary: Category
      operationId: getCategory
      description: Category information with the list of associated advertisers.
      tags:
        - Common Categories
      parameters:
        - $ref: '#/components/parameters/categoryId'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - advertisers
      responses:
        '200':
          description: A category
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/EntityResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        $ref: '#/components/schemas/Category'
              examples:
                '0':
                  value:
                    data:
                      id: ZZZZZZZ
                      name: My category
                      color_id: 7
                      advertisers:
                        - id: XXXXXXXX
                          name: Expert.nl
                          network_id: XXXX
                          network_name: Awin
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
    patch:
      summary: Edit category
      operationId: editCategory
      description: Edit an existing category. You can choose to edit some of the fields. If you edit the advertisers field, the new IDs will replace all previous advertisers.
      tags:
        - Common Categories
      parameters:
        - $ref: '#/components/parameters/categoryId'
      requestBody:
        description: The category to edit
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Name of the category
                  example: My category
                  minLength: 2
                  maxLength: 45
                color_id:
                  type: integer
                  description: Color ID used in the dashboard
                  minimum: 1
                  maximum: 10
                advertisers:
                  type: array
                  description: List of advertiser IDs
                  items:
                    type: string
                    example: xxxxxxxx
                    minLength: 8
                    maxLength: 8
            examples:
              '0':
                value:
                  name: My new category
                  color_id: 7
                  advertisers:
                    - XXXXXXXX
                    - YYYYYYYY
      responses:
        '200':
          description: A category
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/EntityResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        $ref: '#/components/schemas/Category'
              examples:
                '0':
                  value:
                    data:
                      id: ZZZZZZZ
                      name: My category
                      color_id: 7
                      advertisers:
                        - id: XXXXXXXX
                          name: Expert.nl
                          network_id: XXXX
                          network_name: Awin
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
    delete:
      summary: Delete category
      operationId: deleteCategory
      description: Delete an existing category.
      tags:
        - Common Categories
      parameters:
        - $ref: '#/components/parameters/categoryId'
      responses:
        '204':
          description: Done
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/reports/transactions:
    get:
      summary: Transactions
      operationId: listTransactions
      description: |
        List all transactions based on a date range. You can select the type of date used for the date range with the `time_type` parameter. 
        Several filters are available to help you retrieve only the data you need. 
        If you have tens or hundreds of thousands of transactions, you will need to use pagination to avoid timeouts.
      tags:
        - AD Reports
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - $ref: '#/components/parameters/timetype'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - basket
                - event
                - reason
                - network_favicon
                - advertiser_favicon
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/categories'
        - $ref: '#/components/parameters/statuses'
        - $ref: '#/components/parameters/channels'
        - $ref: '#/components/parameters/connections'
        - $ref: '#/components/parameters/devices'
        - $ref: '#/components/parameters/countries'
        - $ref: '#/components/parameters/revenue'
        - $ref: '#/components/parameters/order_amount'
        - $ref: '#/components/parameters/rate'
        - $ref: '#/components/parameters/referrer'
        - $ref: '#/components/parameters/transaction_id'
        - $ref: '#/components/parameters/custom'
        - $ref: '#/components/parameters/order_id'
      responses:
        '200':
          description: An array of transactions
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Transaction'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - id: AAAAAAAAAAAAAA
                        source_id: XXXXXXXXXX
                        network_id: QQQQ
                        network_name: Awin
                        network_favicon: null
                        connection_id: XXXXXX
                        connection_name: Awin US
                        advertiser_id: ZZZZZZ
                        advertiser_name: Fnac FR
                        advertiser_favicon: null
                        program_id: WWWWWWWW
                        order_id: '1111111111111'
                        country: FR
                        clicked_at: null
                        sold_at: '2024-08-04T00:00:00'
                        finalized_at: null
                        referrer: https://mywebsite.com
                        target: http://merchant.com
                        device_id: pc
                        os_id: windows
                        channel_id: XXX
                        channel_name: My Website
                        event: null
                        reason: null
                        customs:
                          - MY_CUSTOMS_ID
                          - null
                          - null
                          - null
                          - null
                        currency: EUR
                        order_amount: 20.4
                        revenue: 1.43
                        rate: 7.01
                        source_currency:
                          currency: EUR
                          order_amount: 20.4
                          revenue: 1.43
                        status_id: confirmed
                        status_updated_at: '2024-08-05T03:59:26Z'
                        is_paid: null
                        paid_updated_at: null
                        coupon:
                          used: true
                          code: FLASH10
                        is_inquiry: null
                        basket_count: 1
                        basket:
                          - id: FFFFFFFFFFFFF
                            source_id: EEEEE
                            source_group: null
                            name: Samsung Galaxy S28
                            currency: EUR
                            price: 20.4
                            quantity: 1
                            price_amount: 20.4
                            type_id: sale
                            rate: 7.01
                            revenue: 1.43
                            source_currency:
                              currency: null
                              price: 20.4
                              price_amount: 20.4
                              revenue: 1.43
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/reports/transactions/{transactionId}:
    get:
      summary: Transaction
      operationId: getTransaction
      description: |
        Retrieve transaction information on a one-off basis using this endpoint. 
        If you need to update a large number of transactions, please use the endpoint that lists all transactions.
      tags:
        - AD Reports
      parameters:
        - $ref: '#/components/parameters/transactionId'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - basket
                - event
                - reason
                - network_favicon
                - advertiser_favicon
      responses:
        '200':
          description: Transaction data
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/EntityResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        $ref: '#/components/schemas/Transaction'
              examples:
                '0':
                  value:
                    data:
                      id: AAAAAAAAAAAAAA
                      source_id: XXXXXXXXXX
                      network_id: QQQQ
                      network_name: Awin
                      network_favicon: null
                      connection_id: XXXXXX
                      connection_name: Awin US
                      advertiser_id: ZZZZZZ
                      advertiser_name: Fnac FR
                      advertiser_favicon: null
                      program_id: WWWWWWWW
                      order_id: '1111111111111'
                      country: FR
                      clicked_at: null
                      sold_at: '2024-08-04T00:00:00'
                      finalized_at: null
                      referrer: https://mywebsite.com
                      target: http://merchant.com
                      device_id: pc
                      os_id: windows
                      channel_id: XXX
                      channel_name: My Website
                      event: null
                      reason: null
                      customs:
                        - MY_CUSTOMS_ID
                        - null
                        - null
                        - null
                        - null
                      currency: EUR
                      order_amount: 20.4
                      revenue: 1.43
                      rate: 7.01
                      source_currency:
                        currency: EUR
                        order_amount: 20.4
                        revenue: 1.43
                      status_id: confirmed
                      status_updated_at: '2024-08-05T03:59:26Z'
                      is_paid: null
                      paid_updated_at: null
                      coupon:
                        used: true
                        code: FLASH10
                      is_inquiry: null
                      basket_count: 1
                      basket:
                        - id: FFFFFFFFFFFFF
                          source_id: EEEEE
                          source_group: null
                          name: Samsung Galaxy S28
                          currency: EUR
                          price: 20.4
                          quantity: 1
                          price_amount: 20.4
                          type_id: sale
                          rate: 7.01
                          revenue: 1.43
                          source_currency:
                            currency: null
                            price: 20.4
                            price_amount: 20.4
                            revenue: 1.43
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/reports/revenues:
    get:
      summary: Revenues
      operationId: listRevenues
      description: |
        List all revenues, including CPC revenues, based on a date range.
      tags:
        - AD Reports
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - network_favicon
                - advertiser_favicon
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/categories'
        - $ref: '#/components/parameters/statuses'
        - $ref: '#/components/parameters/channels'
        - $ref: '#/components/parameters/connections'
        - $ref: '#/components/parameters/countries'
        - $ref: '#/components/parameters/revenue'
        - $ref: '#/components/parameters/transaction_count'
        - $ref: '#/components/parameters/click_count'
      responses:
        '200':
          description: An array of revenues
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Revenue'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - date: '2024-08-15'
                        network_id: XXXX
                        network_name: Awin
                        network_favicon: null
                        connection_id: ZZZZZZZZ
                        connection_name: Awin ES
                        advertiser_id: YYYYYYYY
                        advertiser_name: PcComponentes ES
                        advertiser_favicon: null
                        country: ES
                        channel_id: null
                        channel_name: null
                        status_id: confirmed
                        currency: EUR
                        revenue: 2.36
                        source_currency:
                          currency: GBP
                          revenue: 2.81
                        transaction_count: 1
                        click_count: null
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/reports/clicks:
    get:
      summary: Clicks
      operationId: listClicks
      description: List all clicks based on a date range.
      tags:
        - AD Reports
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - network_favicon
                - advertiser_favicon
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/categories'
        - $ref: '#/components/parameters/channels'
        - $ref: '#/components/parameters/connections'
        - $ref: '#/components/parameters/click_count'
      responses:
        '200':
          description: An array of clicks
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Click'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - date: '2024-08-01'
                        network_id: XXXX
                        network_name: Adrecord
                        network_favicon: null
                        connection_id: IIIIIIII
                        connection_name: Adrecord US
                        advertiser_id: YYYYYYYY
                        advertiser_name: Walmart
                        advertiser_favicon: null
                        channel_id: ZZZZZZZZ
                        channel_name: My Channel
                        click_count: 1
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/reports/payments:
    get:
      summary: Payments
      operationId: listPayments
      description: |
        List all payments based on a date range. Payments are payouts from affiliate networks paid to your bank account.
      tags:
        - AD Reports
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - $ref: '#/components/parameters/timetype'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - network_favicon
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/connections'
        - $ref: '#/components/parameters/amount'
      responses:
        '200':
          description: An array of payments
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Payment'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - billed_at: '2024-04-15'
                        paid_at: '2024-04-15'
                        source_id: YYYYYYY
                        network_id: XXXX
                        network_name: Daisycon
                        network_favicon: null
                        connection_id: UUUUUUUU
                        connection_name: Daisycon US
                        currency: EUR
                        amount: 150
                        source_currency:
                          currency: GBP
                          amount: 168
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/reports/products:
    get:
      summary: Products
      operationId: listProducts
      description: List all products based on transaction quantity and a date range.
      tags:
        - AD Reports
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - items.network_favicon
                - items.advertiser_favicon
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/revenue'
        - $ref: '#/components/parameters/order_amount'
        - $ref: '#/components/parameters/quantity'
      responses:
        '200':
          description: An array of products
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Product'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - name: Samsung Galaxy S29
                        quantity: 1
                        currency: EUR
                        order_amount: 162.31
                        revenue: 4.87
                        items:
                          - network_id: XB9g
                            network_name: Amazon
                            network_favicon: null
                            advertiser_id: 1lWkGWBx
                            advertiser_name: Amazon ES
                            advertiser_favicon: null
                            currency: EUR
                            order_amount: 162.31
                            revenue: 4.87
                            status_id: confirmed
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/statistics:
    get:
      summary: Global statistics
      operationId: listADStatistics
      description: |
        List global statistics based on a date range and type of range. 
        You need to use the `select` parameter to retrieve specific resource properties in a response.
      tags:
        - AD Statistics
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - in: query
          name: select
          description: Select the data to display
          required: true
          schema:
            type: array
            items:
              type: string
              enum:
                - revenues
                - transactions
                - clicks
                - statuses
                - payments
                - performances
        - $ref: '#/components/parameters/timerange'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/statuses'
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/categories'
        - $ref: '#/components/parameters/channels'
        - $ref: '#/components/parameters/connections'
        - $ref: '#/components/parameters/countries'
        - $ref: '#/components/parameters/devices'
      responses:
        '200':
          description: An array of statistics
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          allOf:
                            - type: object
                              required:
                                - date
                              properties:
                                date:
                                  $ref: '#/components/schemas/StatisticDate'
                            - $ref: '#/components/schemas/Statistic'
                            - type: object
                              required:
                                - statuses
                              properties:
                                statuses:
                                  $ref: '#/components/schemas/StatisticStatus'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - date: '2024-07-29'
                        revenue_amount: 114.38
                        transactions:
                          count: 24
                          revenue_amount: 110.58
                          order_amount: 296.51
                        click_count: 184
                        payments:
                          count: 1
                          amount: 77.15
                        performances:
                          rev_share: 83.36
                          epc: 1.25
                          cr: 13.04
                          aov: 95.69
                        statuses:
                          confirmed:
                            revenue_amount: 107.21
                            transaction_count: 16
                          pending:
                            revenue_amount: 17.17
                            transaction_count: 3
                          declined:
                            revenue_amount: 45
                            transaction_count: 1
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/statistics/networks:
    get:
      summary: Network statistics
      operationId: listADStatisticsNetwork
      description: |
        List network statistics based on a date range and type of range. 
        You need to use the `select` parameter to retrieve specific resource properties in a response.
      tags:
        - AD Statistics
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - in: query
          name: select
          description: Select the data to display
          required: true
          schema:
            type: array
            items:
              type: string
              enum:
                - revenues
                - transactions
                - clicks
                - statuses
                - payments
                - performances
        - $ref: '#/components/parameters/timerange'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/statuses'
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/views'
      responses:
        '200':
          description: An array of statistics
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          allOf:
                            - type: object
                              required:
                                - date
                                - network_id
                                - network_name
                              properties:
                                date:
                                  $ref: '#/components/schemas/StatisticDate'
                                network_id:
                                  type: string
                                  description: Network ID
                                  example: xxxx
                                  minLength: 4
                                  maxLength: 4
                                network_name:
                                  type: string
                                  description: Network name
                                  example: TradeTracker
                            - $ref: '#/components/schemas/Statistic'
                            - type: object
                              required:
                                - statuses
                              properties:
                                statuses:
                                  $ref: '#/components/schemas/StatisticStatus'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - date: '2024-07-29'
                        network_id: XXXX
                        network_name: Awin
                        revenue_amount: 114.38
                        transactions:
                          count: 24
                          revenue_amount: 110.58
                          order_amount: 296.51
                        click_count: 184
                        payments:
                          count: 1
                          amount: 77.15
                        performances:
                          rev_share: 83.36
                          epc: 1.25
                          cr: 13.04
                          aov: 95.69
                        statuses:
                          confirmed:
                            revenue_amount: 107.21
                            transaction_count: 16
                          pending:
                            revenue_amount: 17.17
                            transaction_count: 3
                          declined:
                            revenue_amount: 45
                            transaction_count: 1
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/statistics/advertisers:
    get:
      summary: Advertiser statistics
      operationId: listADStatisticsAdvertiser
      description: |
        List advertiser statistics based on a date range and type of range. 
        You need to use the `select` parameter to retrieve specific resource properties in a response.
      tags:
        - AD Statistics
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - in: query
          name: select
          description: Select the data to display
          required: true
          schema:
            type: array
            items:
              type: string
              enum:
                - revenues
                - transactions
                - clicks
                - statuses
                - performances
        - $ref: '#/components/parameters/timerange'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/statuses'
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/categories'
      responses:
        '200':
          description: An array of statistics
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          allOf:
                            - type: object
                              required:
                                - date
                                - advertiser_id
                                - advertiser_name
                                - network_id
                                - network_name
                              properties:
                                date:
                                  $ref: '#/components/schemas/StatisticDate'
                                advertiser_id:
                                  type: string
                                  description: Advertiser ID
                                  example: xxxxxxxx
                                  minLength: 8
                                  maxLength: 8
                                advertiser_name:
                                  type:
                                    - 'null'
                                    - string
                                  description: Advertiser name
                                  example: Expert.nl
                                network_id:
                                  type: string
                                  description: Network ID
                                  example: xxxx
                                  minLength: 4
                                  maxLength: 4
                                network_name:
                                  type: string
                                  description: Network name
                                  example: TradeTracker
                            - $ref: '#/components/schemas/StatisticNoPayment'
                            - type: object
                              required:
                                - statuses
                              properties:
                                statuses:
                                  $ref: '#/components/schemas/StatisticStatus'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - date: '2024-07-29'
                        advertiser_id: XXXXXXXX
                        advertiser_name: Cdiscount FR
                        network_id: YYYY
                        network_name: Awin
                        revenue_amount: 114.38
                        transactions:
                          count: 24
                          revenue_amount: 110.58
                          order_amount: 296.51
                        click_count: 184
                        performances:
                          rev_share: 83.36
                          epc: 1.25
                          cr: 13.04
                          aov: 95.69
                        statuses:
                          confirmed:
                            revenue_amount: 107.21
                            transaction_count: 16
                          pending:
                            revenue_amount: 17.17
                            transaction_count: 3
                          declined:
                            revenue_amount: 45
                            transaction_count: 1
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/statistics/categories:
    get:
      summary: Category statistics
      operationId: listADStatisticsCategories
      description: |
        List category statistics based on a date range and type of range. 
        You need to use the `select` parameter to retrieve specific resource properties in a response.
      tags:
        - AD Statistics
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - in: query
          name: select
          description: Select the data to display
          required: true
          schema:
            type: array
            items:
              type: string
              enum:
                - revenues
                - transactions
                - clicks
                - statuses
                - performances
        - $ref: '#/components/parameters/timerange'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/statuses'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/categories'
      responses:
        '200':
          description: An array of statistics
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          allOf:
                            - type: object
                              required:
                                - date
                                - category_id
                                - category_name
                              properties:
                                date:
                                  $ref: '#/components/schemas/StatisticDate'
                                category_id:
                                  type: string
                                  description: Category ID
                                  example: xxxxxxxx
                                  minLength: 8
                                  maxLength: 8
                                category_name:
                                  type: string
                                  description: Category Name
                                  example: Mobile operators
                            - $ref: '#/components/schemas/StatisticNoPayment'
                            - type: object
                              required:
                                - statuses
                              properties:
                                statuses:
                                  $ref: '#/components/schemas/StatisticStatus'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - date: '2024-07-29'
                        category_id: XXXXXXXX
                        category_name: My Category
                        revenue_amount: 114.38
                        transactions:
                          count: 24
                          revenue_amount: 110.58
                          order_amount: 296.51
                        click_count: 184
                        performances:
                          rev_share: 83.36
                          epc: 1.25
                          cr: 13.04
                          aov: 95.69
                        statuses:
                          confirmed:
                            revenue_amount: 107.21
                            transaction_count: 16
                          pending:
                            revenue_amount: 17.17
                            transaction_count: 3
                          declined:
                            revenue_amount: 45
                            transaction_count: 1
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/statistics/views:
    get:
      summary: View statistics
      operationId: listADStatisticsViews
      description: |
        List view statistics based on a date range and type of range. 
        You need to use the `select` parameter to retrieve specific resource properties in a response.
      tags:
        - AD Statistics
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - in: query
          name: select
          description: Select the data to display
          required: true
          schema:
            type: array
            items:
              type: string
              enum:
                - revenues
                - transactions
                - clicks
                - statuses
                - performances
        - $ref: '#/components/parameters/timerange'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/statuses'
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/categories'
      responses:
        '200':
          description: An array of statistics
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          allOf:
                            - type: object
                              required:
                                - date
                                - view_id
                                - view_name
                              properties:
                                date:
                                  $ref: '#/components/schemas/StatisticDate'
                                view_id:
                                  type: string
                                  description: View ID
                                  example: xxxxxxxx
                                  minLength: 8
                                  maxLength: 8
                                view_name:
                                  type: string
                                  description: Name
                                  example: My View
                            - $ref: '#/components/schemas/Statistic'
                            - type: object
                              required:
                                - statuses
                              properties:
                                statuses:
                                  $ref: '#/components/schemas/StatisticStatus'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - date: '2024-07-29'
                        view_id: XXXXXXXX
                        view_name: MyWebsite.com
                        revenue_amount: 114.38
                        transactions:
                          count: 24
                          revenue_amount: 110.58
                          order_amount: 296.51
                        click_count: 184
                        performances:
                          rev_share: 83.36
                          epc: 1.25
                          cr: 13.04
                          aov: 95.69
                        statuses:
                          confirmed:
                            revenue_amount: 107.21
                            transaction_count: 16
                          pending:
                            revenue_amount: 17.17
                            transaction_count: 3
                          declined:
                            revenue_amount: 45
                            transaction_count: 1
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/statistics/devices:
    get:
      summary: Device statistics
      operationId: listADStatisticsDevice
      description: |
        List device statistics based on a date range and type of range. 
        You need to use the `select` parameter to retrieve specific resource properties in a response.
      tags:
        - AD Statistics
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - in: query
          name: select
          description: Select the data to display
          required: true
          schema:
            type: array
            items:
              type: string
              enum:
                - transactions
                - performances
        - $ref: '#/components/parameters/timerange'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/statuses'
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/categories'
      responses:
        '200':
          description: An array of statistics
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          allOf:
                            - type: object
                              required:
                                - date
                                - device_id
                              properties:
                                date:
                                  $ref: '#/components/schemas/StatisticDate'
                                device_id:
                                  type:
                                    - 'null'
                                    - string
                                  description: Device ID
                                  enum:
                                    - unknown
                                    - pc
                                    - smartphone
                                    - tablet
                            - $ref: '#/components/schemas/StatisticDevice'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - date: '2024-07-29'
                        device_id: pc
                        transactions:
                          count: 24
                          revenue_amount: 110.58
                          order_amount: 296.51
                        performances:
                          rev_share: 83.36
                          epc: 1.25
                          cr: 13.04
                          aov: 95.69
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/statistics/countries:
    get:
      summary: Country statistics
      operationId: listADStatisticsCountry
      description: |
        List country statistics based on a date range and type of range. 
        You need to use the `select` parameter to retrieve specific resource properties in a response.
      tags:
        - AD Statistics
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - in: query
          name: select
          description: Select the data to display
          required: true
          schema:
            type: array
            items:
              type: string
              enum:
                - revenues
                - transactions
                - statuses
                - performances
        - $ref: '#/components/parameters/timerange'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/statuses'
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/categories'
      responses:
        '200':
          description: An array of statistics
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          allOf:
                            - type: object
                              required:
                                - date
                                - country
                              properties:
                                date:
                                  $ref: '#/components/schemas/StatisticDate'
                                country:
                                  type: string
                                  description: Country ISO 3166-1 alpha-2 code
                                  example: NL
                                  minLength: 2
                                  maxLength: 2
                            - $ref: '#/components/schemas/StatisticCountry'
                            - type: object
                              required:
                                - statuses
                              properties:
                                statuses:
                                  $ref: '#/components/schemas/StatisticStatus'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - date: '2024-07-29'
                        country: US
                        revenue_amount: 114.38
                        transactions:
                          count: 24
                          revenue_amount: 110.58
                          order_amount: 296.51
                        performances:
                          rev_share: 83.36
                          epc: 1.25
                          cr: 13.04
                          aov: 95.69
                        statuses:
                          confirmed:
                            revenue_amount: 107.21
                            transaction_count: 16
                          pending:
                            revenue_amount: 17.17
                            transaction_count: 3
                          declined:
                            revenue_amount: 45
                            transaction_count: 1
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/tools/link_builder:
    get:
      summary: Linkbuilder
      operationId: getLinkbuilder
      description: |
        The Linkbuilder endpoint allows you to generate affiliate links from deep links. 
        Before using the API, please read the API section of the [Link Builder's guide](https://app.strackr.com/aa/guides/kwLzej).
      tags:
        - AD Tools
      parameters:
        - in: query
          name: urls
          description: Encoded URLs. You can pass multiple URL parameters, with a maximum of 100 URLs per request.
          required: true
          schema:
            type: array
            maxItems: 100
            items:
              type: string
              format: uri
        - in: query
          name: customs
          description: List of custom parameters you want to add to the tracking links.
          schema:
            type: array
            maxItems: 5
            items:
              type: string
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - commissions
        - in: query
          name: is_deeplink_enabled_only
          description: |
            By default, the link builder creates tracking links only if advertisers have deep links enabled. 
            By using the value `false`, the link builder will also provide tracking links without deep links 
            for advertisers who have deep links disabled.
          schema:
            type: boolean
            default: 'true'
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/connections'
      responses:
        '200':
          description: An array of URLs with advertisers
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Linkbuilder'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - url: https://www.cdiscount.com/maison/v-117-0.html
                        advertisers:
                          - advertiser_id: XXXXXXX
                            advertiser_name: Cdiscount FR
                            network_id: YYYY
                            network_name: Awin
                            is_deeplink_enabled: true
                            connections:
                              - connection_id: UUUUUUU
                                connection_name: Awin FR
                                links:
                                  - channel_id: null
                                    channel_name: null
                                    commissions:
                                      ratio:
                                        min: 0
                                        max: 8
                                      fixed:
                                        min: 0
                                        max: 3
                                      cpc: null
                                    tracking_link: https://www.awin1.com/cread.php?ued=https%3A%2F%2Fwww.cdiscount.com%2Fmaison%2Fv-117-0.html
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/tools/tracking_links:
    get:
      summary: Tracking Links
      operationId: getTrackingLinks
      description: |
        The Tracking Links endpoint allows you to retrieve tracking links from all your advertisers based on the partnership. 
        If you need to create tracking links based on URLs, please check the LinkBuilder endpoint.
      tags:
        - AD Tools
      parameters:
        - $ref: '#/components/parameters/pagerequired'
        - $ref: '#/components/parameters/limitrequired'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - domains
                - commissions
        - in: query
          name: is_deeplink_enabled_only
          description: |
            By default, the link builder creates tracking links only if advertisers have deep links enabled. 
            By using the value `false`, the link builder will also provide tracking links without deep links 
            for advertisers who have deep links disabled.
          schema:
            type: boolean
            default: 'true'
        - in: query
          name: is_pattern_deeplink
          description: |
            This parameter enables replacing the default deep link with a pattern `__DEEPLINK__`. 
            This is useful if you need to dynamically replace the deep link in your application.
          schema:
            type: boolean
            default: 'false'
        - in: query
          name: is_pattern_custom
          description: |
            This parameter enables replacing the default custom parameter with a pattern `__CUSTOM_ID__`. 
            This is useful if you need to dynamically replace the custom parameter in your application.
          schema:
            type: boolean
            default: 'false'
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/connections'
      responses:
        '200':
          description: An array of advertisers
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Trackinglink'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - advertiser_id: XXXXXXX
                        advertiser_name: Cdiscount FR
                        network_id: YYYY
                        network_name: Awin
                        is_deeplink_enabled: true
                        domains:
                          - domain.com
                          - '*.domain.com'
                          - domainbusinessshop.com
                        connections:
                          - connection_id: UUUUUUU
                            connection_name: Awin FR
                            links:
                              - channel_id: null
                                channel_name: null
                                commissions:
                                  ratio:
                                    min: 0
                                    max: 8
                                  fixed:
                                    min: 0
                                    max: 3
                                  cpc: null
                                tracking_link: https://www.awin1.com/cread.php?ued=https%3A%2F%2Fwww.cdiscount.com%2Fmaison%2Fv-117-0.html
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/tools/commission_details:
    get:
      summary: Commission details
      operationId: getCommissionDetails
      description: |
        🔴️ **WORK IN PROGRESS** - The [Commission Details](/aa/guides/NwZE9l) endpoint lists commission information from affiliate networks that provide details. 
        You can also track commission changes with the `previous_commission` and `commission_changed_at` fields. 
        This endpoint is a work in progress; please send us your feedback.
      tags:
        - AD Tools
      parameters:
        - $ref: '#/components/parameters/pagerequired'
        - $ref: '#/components/parameters/limitrequired'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - advertiser_favicon
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/connections'
      responses:
        '200':
          description: An array of commissions
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Commissiondetail'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - id: XXXXXXXXXXXX
                        source_id: '2493295'
                        network_id: YYYY
                        network_name: Effinity
                        connection_id: ZZZZZZZZ
                        connection_name: Effiliation
                        advertiser_id: ZZZZZZZZ
                        advertiser_name: ElectroDepot
                        advertiser_favicon: null
                        channel_id: null
                        channel_name: null
                        name: CPA % postclic
                        description: Business computers
                        status_id: active
                        country: US
                        currency: USD
                        type_id: ratio
                        commission: 9.5
                        commission_tiers:
                          period: month
                          tiers:
                            - name: Step 1
                              type_id: ratio
                              threshold_type_id: transaction_count
                              threshold: 10
                              commission: 12.9
                        commission_conditions:
                          - name: Existing Customer
                            type_id: ratio
                            commission: 1
                          - name: 'New Customer: free sim + bundles'
                            type_id: fixed
                            commission: 5
                        start_at: null
                        end_at: null
                        cookie_hours: 168
                        previous_commission: 11.9
                        commission_changed_at: '2025-04-02T07:10:36Z'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/tools/deals:
    get:
      summary: Deals
      operationId: listDeals
      description: |
        List all deals (coupons and promotions). Several filters are available to help you retrieve only the data you need, such as `statuses`. 
        If you have tens or hundreds of thousands of deals, you will need to use pagination to avoid timeouts.
      tags:
        - AD Tools
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - advertiser_favicon
                - advertiser_website
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/categories'
        - $ref: '#/components/parameters/channels'
        - $ref: '#/components/parameters/connections'
        - $ref: '#/components/parameters/countries'
        - $ref: '#/components/parameters/statusesdeal_statuses'
        - $ref: '#/components/parameters/types'
        - $ref: '#/components/parameters/discount_ratio'
        - $ref: '#/components/parameters/discount_fixed'
      responses:
        '200':
          description: An array of deals
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Deal'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - id: AAAAAAAAAAAAA
                        source_id: '1234567'
                        network_id: FFF
                        network_name: TradeTracker
                        connection_id: XXXXXX
                        connection_name: Awin US
                        advertiser_id: ZZZZZ
                        advertiser_name: Conrad.nl
                        advertiser_favicon: null
                        advertiser_website: https://url.com
                        channel_id: XXX
                        channel_name: My website
                        type_id: coupon
                        title: Algemeen 5%
                        description: Korting op het hele assortiment.
                        status_id: upcoming
                        start_at: '2019-01-02T00:00:00'
                        end_at: '2019-03-31T00:00:00'
                        coupon: CODE
                        discount_ratio: 5
                        discount_fixed: null
                        currency: EUR
                        country: NL
                        deeplink: https://url.com/productpage
                        tracking_link: https://trackinglink.com/xyz
                        terms: null
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /ad/tools/product_feeds:
    get:
      summary: Product feeds
      operationId: getProductFeeds
      description: |
        🔴️ **WORK IN PROGRESS** - This endpoint is a work in progress; please send us your feedback.
        More information in the [dedicated guide](/aa/guides/Nekp9z).
      tags:
        - AD Tools
      parameters:
        - $ref: '#/components/parameters/pagerequired'
        - $ref: '#/components/parameters/limitrequired'
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/connections'
      responses:
        '200':
          description: An array of product feed
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Productfeed'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - id: XXXXXXXXXXXX
                        source_id: '1474078'
                        network_id: YYYY
                        network_name: TradeTracker
                        connection_id: IIIIIIII
                        connection_name: Tradetracker GB
                        advertiser_id: XXXXXXXX
                        advertiser_name: TradeTracker.com
                        channel_id: OOOOOO
                        channel_name: Channel
                        name: Ali Express New Feed 2025
                        status_id: active
                        country: GB
                        currency: GBP
                        url: https://pf.tradetracker.net/?categoryType=2&additionalType=2
                        url_type: http
                        mime_type: application/xml
                        file_size: null
                        is_compressed: false
                        product_count: 104028
                        file_updated_at: '2025-04-15T15:05:46Z'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /im/networks:
    get:
      summary: Networks
      operationId: listImNetworks
      description: |
        List all networks available for your account that have the Inquiry Manager enabled. 
        You can expand the data to display the connections associated with each network.
      tags:
        - IM Networks
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - logo
                - favicon
                - connections
        - $ref: '#/components/parameters/views'
      responses:
        '200':
          description: An array of networks
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/NetworkIM'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - id: XXXX
                        name: Awin
                        logo: null
                        favicon: null
                        connections:
                          - id: ZZZZZZZZ
                            name: Awin US
                            connector_id: XXXXXX
                            connector_name: API
                            is_transaction_inquiries: true
                            is_inquiries: true
                            state: pending
                            upload_type_id: api
                            upload_frequency: manual
                            inquiry_fields:
                              available:
                                - connection_id
                                - advertiser_id
                                - type_id
                                - sold_at
                                - revenue
                                - currency
                                - order_id
                                - order_amount
                                - customer_id
                                - revenue
                                - comment
                                - tracking_url
                              required:
                                - connection_id
                                - advertiser_id
                                - type_id
                                - sold_at
                                - revenue
                                - currency
                                - order_id
                                - order_amount
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /im/reports/transaction_inquiries:
    get:
      summary: Transactions inquiries
      operationId: listTransactionInquiries
      description: |
        List all transaction inquiries based on a date range. 
        Once you have uploaded inquiries from Strackr or network dashboards, affiliate networks create transaction inquiries to monitor statuses.
        If you have tens or hundreds of thousands of transaction inquiries, you will need to use pagination to avoid timeouts.
      tags:
        - IM Transaction inquiries
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - $ref: '#/components/parameters/timetypetransactioninquiry'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - reason
                - network_favicon
                - advertiser_favicon
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/categories'
        - $ref: '#/components/parameters/statuses'
        - $ref: '#/components/parameters/channels'
        - $ref: '#/components/parameters/connections'
        - $ref: '#/components/parameters/expected_revenue'
        - $ref: '#/components/parameters/expected_order_amount'
        - $ref: '#/components/parameters/transaction_inquiry_id'
        - $ref: '#/components/parameters/custom'
        - $ref: '#/components/parameters/order_id'
        - $ref: '#/components/parameters/typestransactioninquiry_types'
      responses:
        '200':
          description: An array of transaction inquiries
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Transactioninquiry'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - id: AAAAAAAAAAAAAA
                        source_id: XXXXXXXXXX
                        network_id: QQQQ
                        network_name: Impact Radius
                        network_favicon: null
                        connection_id: XXXXXX
                        connection_name: Impact
                        advertiser_id: ZZZZZZ
                        advertiser_name: Temu Affiliate Program
                        advertiser_favicon: null
                        channel_id: null
                        channel_name: null
                        type_id: untracked
                        created_at: '2025-01-07T23:25:47'
                        sold_at: '2025-01-01T14:10:40'
                        order_id: PO-069-10498291110952304
                        customer_id: USER_ID
                        currency: EUR
                        expected_order_amount: 35.84
                        expected_revenue: 8.96
                        final_order_amount: null
                        final_revenue: null
                        source_currency:
                          currency: EUR
                          expected_order_amount: 35.84
                          expected_revenue: 8.96
                          final_order_amount: null
                          final_revenue: null
                        customs:
                          - custom_id123
                          - null
                        status_id: declined
                        status_updated_at: '2025-01-12T00:18:31'
                        reason:
                          id: XXXXXXX
                          name: Another market channel was credited with this sale.
                        comment: Transaction declined without reason
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /im/reports/inquiry_uploads:
    get:
      summary: Uploads
      operationId: listUploads
      description: List all uploads based on a date range.
      tags:
        - IM Uploads
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - $ref: '#/components/parameters/timetypeupload'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - network_favicon
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/statusesupload_statuses'
      responses:
        '200':
          description: An array of uploads
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/Upload'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - id: XXXXX
                        network_id: YYYY
                        network_name: Awin
                        network_favicon: null
                        connection_id: IIIIIIII
                        connection_name: Awin
                        type_id: ftp
                        status_id: uploaded
                        range_start_date: null
                        range_end_date: null
                        sending_datetime: null
                        created_at: '2025-02-16T23:11:15Z'
                        uploaded_at: '2025-02-16T23:11:19Z'
                        inquiry_count: 32
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /im/reports/inquiry_uploads/{inquiryUploadId}:
    get:
      summary: Upload
      operationId: getUpload
      description: Upload information.
      tags:
        - IM Uploads
      parameters:
        - $ref: '#/components/parameters/inquiryUploadId'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - network_favicon
      responses:
        '200':
          description: Upload data
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/EntityResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        $ref: '#/components/schemas/Upload'
              examples:
                '0':
                  value:
                    data:
                      id: XXXXX
                      network_id: YYYY
                      network_name: Awin
                      network_favicon: null
                      connection_id: IIIIIIII
                      connection_name: Awin
                      type_id: ftp
                      status_id: uploaded
                      range_start_date: null
                      range_end_date: null
                      sending_datetime: null
                      created_at: '2025-02-16T23:11:15Z'
                      uploaded_at: '2025-02-16T23:11:19Z'
                      inquiry_count: 32
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /im/reports/inquiry_uploads/{inquiryUploadId}/file:
    get:
      summary: Upload file
      operationId: getUploadFile
      description: Download a file containing a list of inquiries when a manual upload to the network dashboard is required.
      tags:
        - IM Uploads
      parameters:
        - $ref: '#/components/parameters/inquiryUploadId'
      responses:
        '200':
          description: File with the list of inquiries
          content:
            application:
              schema:
                type: string
                format: binary
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /im/reports/inquiries:
    get:
      summary: Inquiries
      operationId: listInquiries
      description: List all inquiries you created on Strackr based on a date range.
      tags:
        - IM Inquiries
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - $ref: '#/components/parameters/timetypeinquiry'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - in: query
          name: expand
          description: Expand data
          schema:
            type: array
            items:
              type: string
              enum:
                - network_favicon
                - advertiser_favicon
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/categories'
        - $ref: '#/components/parameters/statuses'
        - $ref: '#/components/parameters/channels'
        - $ref: '#/components/parameters/connections'
        - $ref: '#/components/parameters/revenue'
        - $ref: '#/components/parameters/order_amount'
        - $ref: '#/components/parameters/inquiry_id'
        - $ref: '#/components/parameters/custom'
        - $ref: '#/components/parameters/order_id'
        - $ref: '#/components/parameters/typestransactioninquiry_types'
      responses:
        '200':
          description: An array of inquiries
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/inquiry'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - id: AAAAAAAAAAAAAA
                        source_id: XXXXXXXXXX
                        network_id: QQQQ
                        network_name: Impact Radius
                        network_favicon: null
                        connection_id: XXXXXX
                        connection_name: Impact
                        advertiser_id: ZZZZZZ
                        advertiser_name: Temu Affiliate Program
                        advertiser_favicon: null
                        channel_id: null
                        channel_name: null
                        type_id: untracked
                        created_at: '2025-01-07T23:25:47'
                        sold_at: '2025-01-01T14:10:40'
                        order_id: PO-069-10498291110952304
                        customer_id: USER_ID
                        currency: EUR
                        order_amount: 35.84
                        revenue: 8.96
                        source_currency:
                          currency: EUR
                          order_amount: 35.84
                          revenue: 8.96
                        customs:
                          - custom_id123
                          - null
                        comment: Transaction declined without reason
                        tracking_url: https://track.example.com/
                        status_id: uploaded
                        upload_id: VwBd1
                        error_message: ' Message: Validation failed  Message: Inquiry with OrderId value already exists.'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
    post:
      summary: New inquiry
      operationId: newInquiry
      description: |
        Create one or more inquiries by providing the required fields according to the network. 
        You can find the list of available and required fields in the network endpoint.
        You can create multiple Inquiries with an array of Inquiry object.
      tags:
        - IM Inquiries
      parameters:
        - $ref: '#/components/parameters/currency'
      requestBody:
        description: You can create multiple Inquiries with an array of Inquiry object.
        content:
          application/json:
            schema:
              type: array
              items:
                type: object
                required:
                  - connection_id
                  - advertiser_id
                  - type_id
                  - sold_at
                  - revenue
                  - currency
                properties:
                  connection_id:
                    type: string
                    description: Connection ID
                    minLength: 8
                    maxLength: 8
                  advertiser_id:
                    type: string
                    description: Advertiser ID
                    minLength: 8
                    maxLength: 8
                  channel_id:
                    type: string
                    description: Channel ID
                    minLength: 8
                    maxLength: 8
                  type_id:
                    type: string
                    description: Type ID
                    enum:
                      - unknown
                      - untracked
                      - incorrect
                      - declined
                  sold_at:
                    type: string
                    description: Sold datetime (date ISO 8601)
                    format: date-time
                  order_id:
                    type: string
                    description: Order ID
                  customer_id:
                    type: string
                    description: Customer ID
                  order_amount:
                    type: number
                    format: double
                    description: Expected publisher order amount for the inquiry
                  revenue:
                    type: number
                    format: double
                    minimum: 0.01
                    description: Expected publisher commission for the inquiry
                  currency:
                    type: string
                    description: Currency ISO 4217 code
                    minLength: 3
                    maxLength: 3
                  custom_1:
                    type: string
                    description: Custom ID 1, for example SubIds
                  custom_2:
                    type: string
                    description: Custom ID 2, for example SubIds
                  comment:
                    type: string
                    description: Comment from publisher
                  tracking_url:
                    type: string
                    description: Tracking URL used for the transaction
                    format: uri
              example:
                - connection_id: xxxxxxxx
                  advertiser_id: xxxxxxxx
                  channel_id: xxxxxxxx
                  type_id: untracked
                  sold_at: '2023-09-14T00:00:00'
                  order_id: 3872-0956-FR-1 PP
                  customer_id: user-123456
                  order_amount: 430.32
                  revenue: 56.8
                  currency: EUR
                  custom_1: MY_CUSTOMS_IDS
                  custom_2: null
                  comment: Transaction declined without reason
                  tracking_url: https://mywebsite.com/tracking
      responses:
        '200':
          description: An inquiry
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          $ref: '#/components/schemas/inquiry'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - id: AAAAAAAAAAAAAA
                        source_id: XXXXXXXXXX
                        network_id: QQQQ
                        network_name: Impact Radius
                        network_favicon: null
                        connection_id: XXXXXX
                        connection_name: Impact
                        advertiser_id: ZZZZZZ
                        advertiser_name: Temu Affiliate Program
                        advertiser_favicon: null
                        channel_id: null
                        channel_name: null
                        type_id: untracked
                        created_at: '2025-01-07T23:25:47'
                        sold_at: '2025-01-01T14:10:40'
                        order_id: PO-069-10498291110952304
                        customer_id: USER_ID
                        currency: EUR
                        order_amount: 35.84
                        revenue: 8.96
                        source_currency:
                          currency: EUR
                          order_amount: 35.84
                          revenue: 8.96
                        customs:
                          - custom_id123
                          - null
                        comment: Transaction declined without reason
                        tracking_url: https://track.example.com/
                        status_id: uploaded
                        upload_id: VwBd1
                        error_message: ' Message: Validation failed  Message: Inquiry with OrderId value already exists.'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /im/reports/inquiries/{inquiryId}:
    get:
      summary: Inquiry
      operationId: getInquiry
      description: Inquiry information.
      tags:
        - IM Inquiries
      parameters:
        - $ref: '#/components/parameters/inquiryId'
        - $ref: '#/components/parameters/currency'
      responses:
        '200':
          description: An inquiry
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/EntityResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        $ref: '#/components/schemas/inquiry'
              examples:
                '0':
                  value:
                    data:
                      id: AAAAAAAAAAAAAA
                      source_id: XXXXXXXXXX
                      network_id: QQQQ
                      network_name: Impact Radius
                      network_favicon: null
                      connection_id: XXXXXX
                      connection_name: Impact
                      advertiser_id: ZZZZZZ
                      advertiser_name: Temu Affiliate Program
                      advertiser_favicon: null
                      channel_id: null
                      channel_name: null
                      type_id: untracked
                      created_at: '2025-01-07T23:25:47'
                      sold_at: '2025-01-01T14:10:40'
                      order_id: PO-069-10498291110952304
                      customer_id: USER_ID
                      currency: EUR
                      order_amount: 35.84
                      revenue: 8.96
                      source_currency:
                        currency: EUR
                        order_amount: 35.84
                        revenue: 8.96
                      customs:
                        - custom_id123
                        - null
                      comment: Transaction declined without reason
                      tracking_url: https://track.example.com/
                      status_id: uploaded
                      upload_id: VwBd1
                      error_message: ' Message: Validation failed  Message: Inquiry with OrderId value already exists.'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
    patch:
      summary: Edit inquiry
      operationId: editInquiry
      description: Edit an existing inquiry. You can choose to edit some of the fields.
      tags:
        - IM Inquiries
      parameters:
        - $ref: '#/components/parameters/inquiryId'
        - $ref: '#/components/parameters/currency'
      requestBody:
        description: You can find the list of available and required fields in the network endpoint.
        content:
          application/json:
            schema:
              type: array
              items:
                type: object
                properties:
                  advertiser_id:
                    type: string
                    description: Advertiser ID
                    minLength: 8
                    maxLength: 8
                  channel_id:
                    type: string
                    description: Channel ID
                    minLength: 8
                    maxLength: 8
                  type_id:
                    type: string
                    description: Type ID
                    enum:
                      - unknown
                      - untracked
                      - incorrect
                      - declined
                  sold_at:
                    type: string
                    description: Sold datetime (date ISO 8601)
                    format: date-time
                  order_id:
                    type: string
                    description: Order ID
                  customer_id:
                    type: string
                    description: Customer ID
                  order_amount:
                    type: number
                    format: double
                    minimum: 0.01
                    description: Expected publisher order amount for the inquiry
                  revenue:
                    type: number
                    format: double
                    minimum: 0.01
                    description: Expected publisher commission for the inquiry
                  currency:
                    type: string
                    description: Currency ISO 4217 code
                    minLength: 3
                    maxLength: 3
                  custom_1:
                    type: string
                    description: Custom ID 1, for example SubIds
                  custom_2:
                    type: string
                    description: Custom ID 2, for example SubIds
                  comment:
                    type: string
                    description: Comment from publisher
                  tracking_url:
                    type: string
                    description: Tracking URL used for the transaction
                    format: uri
              example:
                - advertiser_id: xxxxxxxx
                  channel_id: xxxxxxxx
                  type_id: untracked
                  sold_at: '2023-09-14T00:00:00'
                  order_id: 3872-0956-FR-1 PP
                  customer_id: user-123456
                  order_amount: 430.32
                  revenue: 56.8
                  currency: EUR
                  custom_1: MY_CUSTOMS_IDS
                  custom_2: null
                  comment: Transaction declined without reason
                  tracking_url: https://mywebsite.com/tracking
      responses:
        '200':
          description: An inquiry
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/EntityResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        $ref: '#/components/schemas/inquiry'
              examples:
                '0':
                  value:
                    data:
                      id: AAAAAAAAAAAAAA
                      source_id: XXXXXXXXXX
                      network_id: QQQQ
                      network_name: Impact Radius
                      network_favicon: null
                      connection_id: XXXXXX
                      connection_name: Impact
                      advertiser_id: ZZZZZZ
                      advertiser_name: Temu Affiliate Program
                      advertiser_favicon: null
                      channel_id: null
                      channel_name: null
                      type_id: untracked
                      created_at: '2025-01-07T23:25:47'
                      sold_at: '2025-01-01T14:10:40'
                      order_id: PO-069-10498291110952304
                      customer_id: USER_ID
                      currency: EUR
                      order_amount: 35.84
                      revenue: 8.96
                      source_currency:
                        currency: EUR
                        order_amount: 35.84
                        revenue: 8.96
                      customs:
                        - custom_id123
                        - null
                      comment: Transaction declined without reason
                      tracking_url: https://track.example.com/
                      status_id: uploaded
                      upload_id: VwBd1
                      error_message: ' Message: Validation failed  Message: Inquiry with OrderId value already exists.'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
    delete:
      summary: Delete inquiry
      operationId: deleteInquiry
      description: Delete an existing inquiry.
      tags:
        - IM Inquiries
      parameters:
        - $ref: '#/components/parameters/inquiryId'
      responses:
        '204':
          description: Done
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /im/statistics:
    get:
      summary: Global statistics
      operationId: listIMStatistics
      description: |
        List global statistics based on a date range and type of range. 
        You need to use the `select` parameter to retrieve specific resource properties in a response.
      tags:
        - IM Statistics
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - in: query
          name: select
          description: Select the data to display
          required: true
          schema:
            type: array
            items:
              type: string
              enum:
                - transaction_inquiries
                - inquiries
                - inquiry_uploads
                - transaction_inquiry_statuses
                - inquiry_statuses
                - inquiry_upload_statuses
        - $ref: '#/components/parameters/timerange'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/connections'
      responses:
        '200':
          description: An array of statistics
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          allOf:
                            - type: object
                              required:
                                - date
                              properties:
                                date:
                                  $ref: '#/components/schemas/StatisticDateIM'
                            - $ref: '#/components/schemas/StatisticIM'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - date: '2024-07-29'
                        transaction_inquiries:
                          count: 929
                          revenue_amount: 11036.53
                          order_amount: 146207.36
                        inquiries:
                          count: 1224
                          revenue_amount: 198271.2
                          order_amount: 15139.51
                        inquiry_uploads:
                          count: 25
                          inquiry_count: 1224
                        transaction_inquiry_statuses:
                          confirmed:
                            count: 76
                            revenue_amount: 1476.15
                          pending:
                            count: 831
                            revenue_amount: 9334.34
                          declined:
                            count: 22
                            revenue_amount: 226.04
                        inquiry_statuses:
                          pending: null
                          uploaded: 937
                          error: 287
                        inquiry_upload_statuses:
                          pending: null
                          uploaded: 25
                          upload_in_progress: null
                          ready_to_download: null
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /im/statistics/networks:
    get:
      summary: Network statistics
      operationId: listIMStatisticsNetwork
      description: |
        List network statistics based on a date range and type of range. 
        You need to use the `select` parameter to retrieve specific resource properties in a response.
      tags:
        - IM Statistics
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - in: query
          name: select
          description: Select the data to display
          required: true
          schema:
            type: array
            items:
              type: string
              enum:
                - transaction_inquiries
                - inquiries
                - inquiry_uploads
                - transaction_inquiry_statuses
                - inquiry_statuses
                - inquiry_upload_statuses
        - $ref: '#/components/parameters/timerange'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/connections'
      responses:
        '200':
          description: An array of statistics
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          allOf:
                            - type: object
                              required:
                                - date
                                - network_id
                                - network_name
                              properties:
                                date:
                                  $ref: '#/components/schemas/StatisticDateIM'
                                network_id:
                                  type: string
                                  description: Network ID
                                  example: xxxx
                                  minLength: 4
                                  maxLength: 4
                                network_name:
                                  type: string
                                  description: Network name
                                  example: TradeTracker
                            - $ref: '#/components/schemas/StatisticIM'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - date: '2025-01-01'
                        network_id: XXXX
                        network_name: Awin
                        transaction_inquiries:
                          count: 929
                          revenue_amount: 11036.53
                          order_amount: 146207.36
                        inquiries:
                          count: 1224
                          revenue_amount: 198271.2
                          order_amount: 15139.51
                        inquiry_uploads:
                          count: 25
                          inquiry_count: 1224
                        transaction_inquiry_statuses:
                          confirmed:
                            count: 76
                            revenue_amount: 1476.15
                          pending:
                            count: 831
                            revenue_amount: 9334.34
                          declined:
                            count: 22
                            revenue_amount: 226.04
                        inquiry_statuses:
                          pending: null
                          uploaded: 937
                          error: 287
                        inquiry_upload_statuses:
                          pending: null
                          uploaded: 25
                          upload_in_progress: null
                          ready_to_download: null
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /im/statistics/connections:
    get:
      summary: Connection statistics
      operationId: listIMStatisticsConnection
      description: |
        List connection statistics based on a date range and type of range. 
        You need to use the `select` parameter to retrieve specific resource properties in a response.
      tags:
        - IM Statistics
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - in: query
          name: select
          description: Select the data to display
          required: true
          schema:
            type: array
            items:
              type: string
              enum:
                - transaction_inquiries
                - inquiries
                - inquiry_uploads
                - transaction_inquiry_statuses
                - inquiry_statuses
                - inquiry_upload_statuses
        - $ref: '#/components/parameters/timerange'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/connections'
      responses:
        '200':
          description: An array of statistics
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          allOf:
                            - type: object
                              required:
                                - date
                                - connection_id
                                - connection_name
                              properties:
                                date:
                                  $ref: '#/components/schemas/StatisticDateIM'
                                connection_id:
                                  type: string
                                  description: Connection ID
                                  example: xxxxxxxx
                                  minLength: 8
                                  maxLength: 8
                                connection_name:
                                  type: string
                                  description: Connection name
                                  example: TradeTracker
                            - $ref: '#/components/schemas/StatisticIM'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - date: '2025-01-01'
                        connection_id: XXXXXXXX
                        connection_name: Awin Test
                        transaction_inquiries:
                          count: 929
                          revenue_amount: 11036.53
                          order_amount: 146207.36
                        inquiries:
                          count: 1224
                          revenue_amount: 198271.2
                          order_amount: 15139.51
                        inquiry_uploads:
                          count: 25
                          inquiry_count: 1224
                        transaction_inquiry_statuses:
                          confirmed:
                            count: 76
                            revenue_amount: 1476.15
                          pending:
                            count: 831
                            revenue_amount: 9334.34
                          declined:
                            count: 22
                            revenue_amount: 226.04
                        inquiry_statuses:
                          pending: null
                          uploaded: 937
                          error: 287
                        inquiry_upload_statuses:
                          pending: null
                          uploaded: 25
                          upload_in_progress: null
                          ready_to_download: null
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
  /im/statistics/advertisers:
    get:
      summary: Advertiser statistics
      operationId: listIMStatisticsAdvertiser
      description: |
        List advertiser statistics based on a date range and type of range. 
        You need to use the `select` parameter to retrieve specific resource properties in a response.
      tags:
        - IM Statistics
      parameters:
        - $ref: '#/components/parameters/timestart'
        - $ref: '#/components/parameters/timeend'
        - in: query
          name: select
          description: Select the data to display
          required: true
          schema:
            type: array
            items:
              type: string
              enum:
                - transaction_inquiries
                - inquiries
                - transaction_inquiry_statuses
                - inquiry_statuses
        - $ref: '#/components/parameters/timerange'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/networks'
        - $ref: '#/components/parameters/advertisers'
        - $ref: '#/components/parameters/views'
        - $ref: '#/components/parameters/connections'
      responses:
        '200':
          description: An array of statistics
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/CollectionResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          allOf:
                            - type: object
                              required:
                                - date
                                - advertiser_id
                                - advertiser_name
                                - network_id
                                - network_name
                              properties:
                                date:
                                  $ref: '#/components/schemas/StatisticDateIM'
                                advertiser_id:
                                  type: string
                                  description: Advertiser ID
                                  example: xxxxxxxx
                                  minLength: 8
                                  maxLength: 8
                                advertiser_name:
                                  type:
                                    - 'null'
                                    - string
                                  description: Advertiser name
                                  example: Expert.nl
                                network_id:
                                  type: string
                                  description: Network ID
                                  example: xxxx
                                  minLength: 4
                                  maxLength: 4
                                network_name:
                                  type: string
                                  description: Network name
                                  example: TradeTracker
                            - $ref: '#/components/schemas/StatisticNoInquiryUpload'
              examples:
                '0':
                  value:
                    pagination: null
                    total_items: 1
                    totals: null
                    data:
                      - date: '2025-01-01'
                        advertiser_id: XXXXXXXX
                        advertiser_name: parapharmacie-et-medicament.com
                        network_id: YYYY
                        network_name: Effinity
                        transaction_inquiries:
                          count: 929
                          revenue_amount: 11036.53
                          order_amount: 146207.36
                        inquiries:
                          count: 1224
                          revenue_amount: 198271.2
                          order_amount: 15139.51
                        inquiry_uploads:
                          count: 25
                          inquiry_count: 1224
                        transaction_inquiry_statuses:
                          confirmed:
                            count: 76
                            revenue_amount: 1476.15
                          pending:
                            count: 831
                            revenue_amount: 9334.34
                          declined:
                            count: 22
                            revenue_amount: 226.04
                        inquiry_statuses:
                          pending: null
                          uploaded: 937
                          error: 287
                        inquiry_upload_statuses:
                          pending: null
                          uploaded: 25
                          upload_in_progress: null
                          ready_to_download: null
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        default:
          $ref: '#/components/responses/UnexpectedError'
components:
  parameters:
    page:
      in: query
      name: page
      schema:
        type: integer
        minimum: 1
        maximum: 999999
      description: The page number
    limit:
      in: query
      name: limit
      schema:
        type: integer
        minimum: 1
        maximum: 10000
      description: The numbers of items to return
    networks:
      name: networks
      in: query
      description: Network IDs
      required: false
      schema:
        type: array
        items:
          type: string
          minLength: 4
          maxLength: 4
      style: form
      explode: false
    views:
      in: query
      name: views
      description: View IDs
      schema:
        type: array
        items:
          type: string
          minLength: 8
          maxLength: 8
      style: form
      explode: false
    categories:
      in: query
      name: categories
      description: Category IDs
      schema:
        type: array
        items:
          type: string
          minLength: 8
          maxLength: 8
      style: form
      explode: false
    countries:
      in: query
      name: countries
      description: Country ISO 3166-1 alpha-2 codes
      schema:
        type: array
        items:
          type: string
          minLength: 2
          maxLength: 2
      style: form
      explode: false
    connections:
      in: query
      name: connections
      description: Connection IDs
      schema:
        type: array
        items:
          type: string
          minLength: 8
          maxLength: 8
      style: form
      explode: false
    advertiserId:
      name: advertiserId
      in: path
      required: true
      description: The id of the advertiser to retrieve
      schema:
        type: string
        minLength: 8
        maxLength: 8
    timestartoptional:
      in: query
      name: time_start
      description: Start date, must be used together with `time_end`.
      schema:
        type: string
        format: date
        example: '2022-07-18'
    timeendoptional:
      in: query
      name: time_end
      description: End date, must be used together with `time_start`.
      schema:
        type: string
        format: date
        example: '2022-07-25'
    channels:
      in: query
      name: channels
      description: Channel IDs
      schema:
        type: array
        items:
          type: string
          minLength: 8
          maxLength: 8
      style: form
      explode: false
    statusesprogram:
      name: statuses
      in: query
      description: Status IDs
      required: false
      schema:
        type: array
        items:
          type: string
          enum:
            - not_joined
            - joined
            - pending
            - suspended
            - rejected
            - closed
      style: form
      explode: false
    networkId:
      name: networkId
      in: path
      required: true
      description: The id of the network to retrieve
      schema:
        type: string
        minLength: 4
        maxLength: 4
    categoryId:
      name: categoryId
      in: path
      required: true
      description: The id of the category to retrieve
      schema:
        type: string
        minLength: 8
        maxLength: 8
    timestart:
      in: query
      name: time_start
      required: true
      description: Start date (format `YYYY-MM-DD`), must be used together with `time_end`.
      schema:
        type: string
        format: date
        example: '2022-07-18'
    timeend:
      in: query
      name: time_end
      required: true
      description: End date (format `YYYY-MM-DD`), must be used together with `time_start`.
      schema:
        type: string
        format: date
        example: '2022-07-25'
    timetype:
      in: query
      name: time_type
      description: Defines the event on which `time_start` and `time_end` shall operate (transaction date, status change date or payment date)
      schema:
        type: string
        enum:
          - sold_at
          - status_updated_at
          - paid_updated_at
        default: sold_at
    currency:
      in: query
      name: currency
      description: Currency of prices (Currency ISO 4217 code)
      schema:
        type: string
        minLength: 3
        maxLength: 3
        enum:
          - EUR
          - USD
          - CAD
          - GBP
          - RUB
          - SEK
          - AUD
          - INR
          - NOK
          - DKK
        default: EUR
      style: form
      explode: false
    advertisers:
      in: query
      name: advertisers
      description: Advertiser IDs
      schema:
        type: array
        items:
          type: string
          minLength: 8
          maxLength: 8
      style: form
      explode: false
    statuses:
      name: statuses
      in: query
      description: Status IDs
      required: false
      schema:
        type: array
        items:
          type: string
          enum:
            - confirmed
            - pending
            - declined
      style: form
      explode: false
    devices:
      name: devices
      in: query
      description: Device IDs
      required: false
      schema:
        type: array
        items:
          type: string
          enum:
            - unknown
            - pc
            - smartphone
            - tablet
      style: form
      explode: false
    revenue:
      in: query
      name: revenue
      description: Revenue
      schema:
        type: number
    order_amount:
      in: query
      name: order_amount
      description: Order Amount
      schema:
        type: number
    rate:
      in: query
      name: rate
      description: Rate
      schema:
        type: number
    referrer:
      in: query
      name: referrer
      description: Referrer
      schema:
        type: string
        minLength: 3
    transaction_id:
      in: query
      name: transaction_id
      description: Transaction ID
      schema:
        type: string
        minLength: 3
    custom:
      in: query
      name: custom
      description: Custom
      schema:
        type: string
        minLength: 1
    order_id:
      in: query
      name: order_id
      description: Order ID
      schema:
        type: string
        minLength: 3
    transactionId:
      name: transactionId
      in: path
      required: true
      description: The id of the transaction to retrieve
      schema:
        type: string
        minLength: 16
        maxLength: 16
    transaction_count:
      in: query
      name: transaction_count
      description: Total transactions
      schema:
        type: integer
        format: int32
    click_count:
      in: query
      name: click_count
      description: Total clicks
      schema:
        type: integer
        format: int32
    amount:
      in: query
      name: amount
      description: Amount
      schema:
        type: number
    quantity:
      in: query
      name: quantity
      description: Quantity
      schema:
        type: integer
        format: int32
    timerange:
      in: query
      name: time_range
      description: Define the time range, the `date` format will depend of this selection
      schema:
        type: string
        enum:
          - day
          - week
          - month
          - year
          - all
        default: day
    pagerequired:
      in: query
      name: page
      required: true
      schema:
        type: integer
        minimum: 1
        maximum: 999999
      description: The page number
    limitrequired:
      in: query
      name: limit
      required: true
      schema:
        type: integer
        minimum: 1
        maximum: 10000
      description: The numbers of items to return
    statusesdeal_statuses:
      name: statuses
      in: query
      description: Status IDs
      required: false
      schema:
        type: array
        items:
          type: string
          enum:
            - active
            - upcoming
            - expired
            - removed
      style: form
      explode: false
    types:
      name: types
      in: query
      description: Type IDs
      required: false
      schema:
        type: array
        items:
          type: string
          enum:
            - coupon
            - promotion
      style: form
      explode: false
    discount_ratio:
      in: query
      name: discount_ratio
      description: Discount ratio
      schema:
        type: number
    discount_fixed:
      in: query
      name: discount_fixed
      description: Discount fixed
      schema:
        type: number
    timetypetransactioninquiry:
      in: query
      name: time_type
      description: Defines the event on which `time_start` and `time_end` shall operate (transaction date, status change date or payment date)
      schema:
        type: string
        enum:
          - sold_at
          - created_at
          - status_updated_at
        default: created_at
    expected_revenue:
      in: query
      name: expected_revenue
      description: Expected revenue
      schema:
        type: number
    expected_order_amount:
      in: query
      name: expected_order_amount
      description: Expected Order Amount
      schema:
        type: number
    transaction_inquiry_id:
      in: query
      name: transaction_inquiry_id
      description: Transaction Inquiry ID
      schema:
        type: string
        minLength: 3
    typestransactioninquiry_types:
      name: types
      in: query
      description: Type IDs
      required: false
      schema:
        type: array
        items:
          type: string
          enum:
            - unknown
            - untracked
            - incorrect
            - declined
      style: form
      explode: false
    timetypeupload:
      in: query
      name: time_type
      description: Defines the event on which `time_start` and `time_end` shall operate (transaction date, status change date or payment date)
      schema:
        type: string
        enum:
          - created_at
          - uploaded_at
        default: created_at
    statusesupload_statuses:
      name: statuses
      in: query
      description: Status IDs
      required: false
      schema:
        type: array
        items:
          type: string
          enum:
            - pending
            - upload_in_progress
            - ready_to_download
            - uploaded
      style: form
      explode: false
    inquiryUploadId:
      name: inquiryUploadId
      in: path
      required: true
      description: The id of the upload to retrieve
      schema:
        type: string
        minLength: 5
        maxLength: 5
    timetypeinquiry:
      in: query
      name: time_type
      description: Defines the event on which `time_start` and `time_end` shall operate (transaction date, status change date or payment date)
      schema:
        type: string
        enum:
          - sold_at
          - created_at
        default: created_at
    inquiry_id:
      in: query
      name: inquiry_id
      description: Inquiry ID
      schema:
        type: string
        minLength: 3
    inquiryId:
      name: inquiryId
      in: path
      required: true
      description: The id of the inquiry to retrieve
      schema:
        type: string
        minLength: 10
        maxLength: 10
    timecomparison:
      in: query
      name: time_comparison
      description: Comparison period
      schema:
        type: string
        enum:
          - period
          - week
          - month
          - quarter
          - year
  schemas:
    Error:
      type: object
      required:
        - details
      properties:
        details:
          type: object
          required:
            - message
            - errors
          properties:
            message:
              type: string
              description: Error message
              example: One or more parameters of the query are not valid.
            errors:
              type: array
              description: List of errors
              items:
                type: object
                required:
                  - field
                  - reason
                  - value
                properties:
                  field:
                    type: string
                    description: Field with the error
                    example: categories
                  reason:
                    type: string
                    description: Reason of the error
                    example: Wrong ID
                  value:
                    anyOf:
                      - type: 'null'
                      - type: integer
                      - type: string
                      - type: boolean
                      - type: object
                      - type: array
                    description: Value who triggered the error
                    example: 123456
    CollectionResponse:
      type: object
      description: Collection of resulst
      required:
        - pagination
        - total_items
        - totals
        - data
      properties:
        pagination:
          type:
            - 'null'
            - object
          description: Pagination information, null if no pagination
          required:
            - page_current
            - page_size
            - page_total
            - has_more
          properties:
            page_current:
              type: integer
              description: Current page (from `page` parameter)
              format: int32
              example: 1
            page_size:
              type: integer
              description: Page size (from `limit` parameter)
              format: int32
              example: 10
            page_total:
              type: integer
              description: Total of page available
              format: int32
              example: 207
            has_more:
              type: boolean
              description: True if you can continue to paging
        total_items:
          type: integer
          description: Total of items in the data array
          format: int32
          example: 243
    Commissions:
      type:
        - 'null'
        - object
      description: Commission informations fo the program (from `expand` parameter)
      required:
        - ratio
        - fixed
        - cpc
      properties:
        ratio:
          type:
            - 'null'
            - object
          description: Ratio (%)
          required:
            - min
            - max
          properties:
            min:
              type:
                - 'null'
                - number
              description: Mininum value
              example: 10
            max:
              type:
                - 'null'
                - number
              description: Maximum value
              example: 16
        fixed:
          type:
            - 'null'
            - object
          description: Fixed
          required:
            - min
            - max
          properties:
            min:
              type:
                - 'null'
                - number
              description: Mininum value
              example: 5
            max:
              type:
                - 'null'
                - number
              description: Maximum value
              example: 5
        cpc:
          type:
            - 'null'
            - object
          description: CPC
          required:
            - min
            - max
          properties:
            min:
              type:
                - 'null'
                - number
              description: Mininum value
              example: 0.1
            max:
              type:
                - 'null'
                - number
              description: Maximum value
              example: 0.12
    Advertiser:
      type: object
      required:
        - id
        - name
        - network_id
        - network_name
        - logo
        - favicon
        - website
        - country
        - source_id
        - cookie_hours
        - status_id
        - features
        - categories
        - commissions
        - programs
      properties:
        id:
          type: string
          description: Advertiser ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        name:
          type: string
          description: Name
          example: Currys PC World
        network_id:
          type: string
          description: Network ID
          example: xxxx
          minLength: 4
          maxLength: 4
        network_name:
          type: string
          description: Network name
          example: Awin
        logo:
          type:
            - 'null'
            - string
          description: Logo URL (from `expand` parameter)
          format: uri
          example: https://i.strackr.com/merchants/amazon-fr.png
        favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://favicon.strackr.com/?sz=32&domain=url.com
        website:
          type:
            - 'null'
            - string
          description: Website URL (from `expand` parameter)
          format: uri
          example: https://url.com
        country:
          type:
            - 'null'
            - string
          description: Country ISO 3166-1 alpha-2 code
          example: GB
        source_id:
          type: string
          description: Advertiser ID from the network.
          example: 123456
        status_id:
          type:
            - 'null'
            - string
          description: Status ID (from `expand` parameter)
          enum:
            - notjoined
            - joined
            - pending
            - suspended
            - rejected
            - closed
        features:
          type:
            - 'null'
            - array
          description: Features (from `expand` parameter)
          items:
            type: object
            required:
              - cookie_hours
              - is_deeplink_enabled
            properties:
              cookie_hours:
                type:
                  - 'null'
                  - integer
                description: Cookie duration in hours
                format: int32
                example: 10
              is_deeplink_enabled:
                type:
                  - 'null'
                  - boolean
                description: Is deeplink enabled for this advertiser, if information is not provided the value is `null`
        categories:
          type:
            - 'null'
            - array
          description: Categories (from `expand` parameter)
          items:
            type: object
            required:
              - id
              - name
              - color_id
            properties:
              id:
                type: string
                description: Category ID
                example: xxxxxxxx
                minLength: 8
                maxLength: 8
              name:
                type: string
                description: Category name
                example: Mobile operators
              color_id:
                type: integer
                description: Color id of the category
                minimum: 1
                maximum: 10
        commissions:
          $ref: '#/components/schemas/Commissions'
        programs:
          type:
            - 'null'
            - array
          description: Programs (from `expand` parameter)
          items:
            type: object
            required:
              - id
              - source_id
              - connection_id
              - connection_name
              - channel_id
              - channel_name
              - status_id
              - status_updated_at
              - commissions
            properties:
              id:
                type:
                  - 'null'
                  - string
                description: Program ID
                example: xxxxxxxx
                minLength: 8
                maxLength: 8
              source_id:
                type:
                  - 'null'
                  - string
                description: Program source ID
              connection_id:
                type: string
                description: Connection ID
                example: xxxxxxxx
                minLength: 8
                maxLength: 8
              connection_name:
                type: string
                description: Connection name
                example: Awin Account A
              channel_id:
                type:
                  - 'null'
                  - string
                description: Channel ID
                example: xxxxxxxx
                minLength: 8
                maxLength: 8
              channel_name:
                type:
                  - 'null'
                  - string
                description: Channel ID
                example: My website
              status_id:
                type: string
                description: Status ID
                enum:
                  - notjoined
                  - joined
                  - pending
                  - suspended
                  - rejected
                  - closed
              status_updated_at:
                type:
                  - 'null'
                  - string
                format: date-time
                description: Status update date (ISO 8601)
                example: '2022-08-08T15:30:55'
              commissions:
                $ref: '#/components/schemas/Commissions'
    EntityResponse:
      type: object
      description: Entity response
      required:
        - data
      properties:
        data:
          type: object
    Program:
      type: object
      required:
        - id
        - source_id
        - advertiser_id
        - advertiser_source_id
        - advertiser_name
        - advertiser_favicon
        - country
        - network_id
        - network_name
        - connection_id
        - connection_name
        - channel_id
        - channel_name
        - status_id
        - status_updated_at
        - commissions
      properties:
        id:
          type:
            - 'null'
            - string
          description: Program ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        source_id:
          type:
            - 'null'
            - string
          description: Program source ID
        advertiser_id:
          type: string
          description: Advertiser ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        advertiser_source_id:
          type: string
          description: Advertiser ID from the network.
          example: 123456
        advertiser_name:
          type: string
          description: Name
          example: Currys PC World
        advertiser_favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://favicon.strackr.com/?sz=32&domain=url.com
        country:
          type:
            - 'null'
            - string
          description: Country ISO 3166-1 alpha-2 code
          example: GB
        network_id:
          type: string
          description: Network ID
          example: xxxx
          minLength: 4
          maxLength: 4
        network_name:
          type: string
          description: Network name
          example: Awin
        connection_id:
          type: string
          description: Connection ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        connection_name:
          type: string
          description: Connection name
          example: Awin Account A
        channel_id:
          type:
            - 'null'
            - string
          description: Channel ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        channel_name:
          type:
            - 'null'
            - string
          description: Channel ID
          example: My website
        status_id:
          type: string
          description: Status ID
          enum:
            - notjoined
            - joined
            - pending
            - suspended
            - rejected
            - closed
        status_updated_at:
          type:
            - 'null'
            - string
          format: date-time
          description: Status update date (ISO 8601)
          example: '2022-08-08T15:30:55'
        commissions:
          $ref: '#/components/schemas/Commissions'
    Network:
      type: object
      required:
        - id
        - name
        - logo
        - favicon
        - connections
      properties:
        id:
          type: string
          description: Network ID
          example: xxxx
          minLength: 4
          maxLength: 4
        name:
          type: string
          description: Network name
          example: Awin
        logo:
          type:
            - 'null'
            - string
          description: Logo URL (from `expand` parameter)
          format: uri
          example: https://i.strackr.com/networks/awin.png
        favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://i.strackr.com/networks/favicons/awin.png
        connections:
          type:
            - 'null'
            - array
          description: Connection list (from `expand` parameter)
          items:
            type: object
            required:
              - id
              - name
              - connector_id
              - connector_name
              - state
              - data
              - features
              - statuses
            properties:
              id:
                type: string
                description: Connection ID
                example: xxxxxxxx
                minLength: 8
                maxLength: 8
              name:
                type: string
                description: Name of the connection (from user)
                example: Awin FR
              connector_id:
                type: string
                description: Connector ID
                example: xxxxxx
                minLength: 6
                maxLength: 6
              connector_name:
                type: string
                description: Name of the connector
                example: API Transactions
              state:
                type: string
                description: State ID
                enum:
                  - pending
                  - in_progress
                  - closed
                  - paused
              data:
                type:
                  - 'null'
                  - object
                description: Data collected from network (from `expand` parameter)
                required:
                  - is_advertisers
                  - is_program_statuses
                  - is_transactions
                  - is_revenues
                  - is_clicks
                  - is_payments
                  - is_deals
                  - is_channels
                properties:
                  is_advertisers:
                    type: boolean
                    description: Advertiser data
                  is_program_statuses:
                    type: boolean
                    description: Advertisers' Programs status data
                  is_transactions:
                    type: boolean
                    description: Transaction data
                  is_revenues:
                    type: boolean
                    description: Revenue data
                  is_clicks:
                    type: boolean
                    description: Click data
                  is_payments:
                    type: boolean
                    description: Payment data
                  is_deals:
                    type: boolean
                    description: Deal data
                  is_channels:
                    type: boolean
                    description: Channel data
              features:
                type:
                  - 'null'
                  - object
                description: Features and tools (from `expand` parameter)
                required:
                  - is_linkbuilder
                  - is_inquiry_manager
                properties:
                  is_linkbuilder:
                    type: boolean
                    description: Link Builder
                  is_inquiry_manager:
                    type: boolean
                    description: Inquiry Manager
              statuses:
                type:
                  - 'null'
                  - object
                description: Informations about updates (from `expand` parameter)
                required:
                  - updated_at
                  - updated_complete_at
                  - updated_statuses_at
                  - last_update_state
                properties:
                  updated_at:
                    type:
                      - 'null'
                      - string
                    format: date-time
                    description: Last update (ISO 8601)
                    example: '2022-08-08T15:30:55'
                  updated_complete_at:
                    type:
                      - 'null'
                      - string
                    format: date-time
                    description: Last complete update (exclude 10 min. update) (ISO 8601)
                    example: '2022-08-08T15:30:55'
                  updated_statuses_at:
                    type:
                      - 'null'
                      - string
                    format: date-time
                    description: Last status update (ISO 8601)
                    example: '2022-08-08T15:30:55'
                  last_update_state:
                    type:
                      - 'null'
                      - string
                    description: State ID
                    enum:
                      - done
                      - error_connection
                      - error_network
    Channel:
      type: object
      required:
        - id
        - name
        - source_id
        - network_id
        - network_name
        - connection_id
        - connection_name
      properties:
        id:
          type: string
          description: Channel ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        name:
          type: string
          description: Name
          example: MyWebsite.nl
        source_id:
          type: string
          description: Channel ID from the network.
          example: aqszxfe
        network_id:
          type: string
          description: Network ID
          example: xxxx
          minLength: 4
          maxLength: 4
        network_name:
          type: string
          description: Name of the network
          example: TradeTracker
        connection_id:
          type: string
          description: Connection ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        connection_name:
          type: string
          description: Name of the connection (from user)
          example: Tradetracker NL
    View:
      type: object
      required:
        - id
        - name
        - connections
        - channels
        - categories
      properties:
        id:
          type: string
          description: View ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        name:
          type: string
          description: Name
          example: My View
        connections:
          type: array
          description: Connections
          items:
            type: object
            required:
              - id
              - name
            properties:
              id:
                type: string
                description: Connection ID
                example: xxxxxxxx
                minLength: 8
                maxLength: 8
              name:
                type: string
                description: Name of the connection (from user)
                example: Awin Website A
        channels:
          type: array
          description: Channels
          items:
            type: object
            required:
              - id
              - name
            properties:
              id:
                type: string
                description: Channel ID
                example: xxxxxxxx
                minLength: 8
                maxLength: 8
              name:
                type: string
                description: Name of the channel
                example: Channel A
        categories:
          type: array
          description: Categories
          items:
            type: object
            required:
              - id
              - name
            properties:
              id:
                type: string
                description: Category ID
                example: xxxxxxxx
                minLength: 8
                maxLength: 8
              name:
                type: string
                description: Name of the category
                example: Category A
    Category:
      type: object
      required:
        - id
        - name
        - color_id
        - advertisers
      properties:
        id:
          type: string
          description: Category ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        name:
          type: string
          description: Name
          example: My Category
        color_id:
          type: integer
          description: Color id of the category
          minimum: 1
          maximum: 10
        advertisers:
          type:
            - 'null'
            - array
          description: Advertisers (from `expand` parameter)
          items:
            type: object
            required:
              - id
              - name
              - network_id
              - network_name
            properties:
              id:
                type: string
                description: Advertiser ID
                example: xxxxxxxx
                minLength: 8
                maxLength: 8
              name:
                type: string
                description: Name of the Advertiser
                example: Expert.nl
              network_id:
                type: string
                description: Network ID
                example: xxxx
                minLength: 4
                maxLength: 4
              network_name:
                type: string
                description: Network name
                example: TradeTracker
    Transaction:
      type: object
      required:
        - id
        - source_id
        - network_id
        - network_name
        - network_favicon
        - connection_id
        - connection_name
        - advertiser_id
        - advertiser_name
        - advertiser_favicon
        - program_id
        - order_id
        - country
        - clicked_at
        - sold_at
        - finalized_at
        - referrer
        - target
        - device_id
        - os_id
        - channel_id
        - channel_name
        - event
        - reason
        - customs
        - currency
        - order_amount
        - revenue
        - rate
        - source_currency
        - status_id
        - status_updated_at
        - is_paid
        - paid_updated_at
        - coupon
        - is_inquiry
        - basket_count
        - basket
      properties:
        id:
          type: string
          description: Transaction ID
          example: xxxxxxxxxxxxxxxx
          minLength: 16
          maxLength: 16
        source_id:
          type:
            - 'null'
            - string
          description: Transaction ID from the network
          example: 861P64916
        network_id:
          type: string
          description: Network ID
          example: xxxx
          minLength: 4
          maxLength: 4
        network_name:
          type: string
          description: Network name
          example: TradeTracker
        network_favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://i.strackr.com/networks/favicons/awin.png
        connection_id:
          type: string
          description: Connection ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        connection_name:
          type: string
          description: Name of the connection (from user)
          example: Tradetracker NL
        advertiser_id:
          type:
            - 'null'
            - string
          description: Advertiser ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        advertiser_name:
          type:
            - 'null'
            - string
          description: Advertiser name
          example: Expert.nl
        advertiser_favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://favicon.strackr.com/?sz=32&domain=url.com
        program_id:
          type:
            - 'null'
            - string
          description: Program ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        order_id:
          type:
            - 'null'
            - string
          description: Order ID from the advertiser
          example: P64916
        country:
          type: string
          description: Country ISO 3166-1 alpha-2 code
          example: NL
          minLength: 2
          maxLength: 2
        clicked_at:
          type:
            - 'null'
            - string
          description: Click user datetime (ISO 8601)
          format: date-time
          example: '2022-08-03T15:25:49'
        sold_at:
          type: string
          description: Transaction datetime (ISO 8601)
          format: date-time
          example: '2022-08-07T12:24:49'
        finalized_at:
          type:
            - 'null'
            - string
          description: Transaction checked (validation, locking, modified, etc...) datetime (ISO 8601) provided by the network, the datetime can be in the future
          format: date-time
          example: '2022-08-22T10:41:12'
        referrer:
          type:
            - 'null'
            - string
          description: Referrer link
          format: uri
          example: https://mywebsite.com
        target:
          type:
            - 'null'
            - string
          description: Target link
          format: uri
          example: https://merchant.com
        device_id:
          type:
            - 'null'
            - string
          description: Device ID
          enum:
            - unknown
            - pc
            - smartphone
            - tablet
        os_id:
          type:
            - 'null'
            - string
          description: OS ID
          enum:
            - unknown
            - windows
            - linux
            - android
            - ios
            - mac
            - blackberry
            - chromeos
        channel_id:
          type:
            - 'null'
            - string
          description: Channel ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        channel_name:
          type:
            - 'null'
            - string
          description: Channel name
          example: My website
        event:
          type:
            - 'null'
            - object
          description: Event property (from `expand` parameter)
          required:
            - id
            - name
          properties:
            id:
              type: string
              description: Event ID
              example: XXXXXXXX
              minLength: 8
              maxLength: 8
            name:
              type: string
              description: Event name
              example: Pre-Pay Check & Reserve
        reason:
          type:
            - 'null'
            - object
          description: Reason property (from `expand` parameter)
          required:
            - id
            - name
          properties:
            id:
              type: string
              description: Reason ID
              example: XXXXXXXX
              minLength: 8
              maxLength: 8
            name:
              type: string
              description: Reason name
              example: Full Refund
        customs:
          type: array
          description: Customs IDs, for example SubIds. You can get 5 customs maximum.
          minItems: 5
          maxItems: 5
          items:
            type: string
            description: A Custom ID
            example: MY_CUSTOMS_IDS
        currency:
          type: string
          description: Currency ISO 4217 code
          example: EUR
          minLength: 3
          maxLength: 3
        order_amount:
          type: number
          description: Amount of the transaction
          example: 561.42
        revenue:
          type: number
          description: Revenue from the transaction
          example: 14.04
        rate:
          type:
            - 'null'
            - number
          description: Rate in percent
          example: 2.5
        source_currency:
          type: object
          description: Currency source information from network
          required:
            - currency
            - order_amount
            - revenue
          properties:
            currency:
              type: string
              description: Currency ISO 4217 code
              example: EUR
              minLength: 3
              maxLength: 3
            order_amount:
              type: number
              description: Amount of the transaction
              example: 499
            revenue:
              type: number
              description: Revenue from the transaction
              example: 12.48
        status_id:
          type: string
          description: Status ID
          enum:
            - pending
            - declined
            - confirmed
        status_updated_at:
          type:
            - 'null'
            - string
          description: Status updated datetime (ISO 8601), from pending to declined or confirmed on Strackr. For network datetime, see `checked` field
          format: date-time
          example: '2022-08-24T14:57:42'
        is_paid:
          type:
            - 'null'
            - boolean
          description: Is paid, null if the network doesn't provide the information
        paid_updated_at:
          type:
            - 'null'
            - string
          description: Paid updated datetime (ISO 8601) on Strackr
          format: date-time
        coupon:
          type:
            - 'null'
            - object
          description: Coupon/Voucher information, null if the network doesn't provide the information
          required:
            - used
            - code
          properties:
            used:
              type: boolean
              description: Is Coupon/Voucher code used
            code:
              type:
                - 'null'
                - string
              description: Coupon/Voucher code
              example: FLASH10
        is_inquiry:
          type:
            - 'null'
            - boolean
          description: Is an inquiry, null if the network doesn't provide the information
        basket_count:
          type: integer
          description: Number of product in the basket field
          format: int32
          example: 10
        basket:
          type: array
          items:
            type: object
            description: A basket (from `expand` parameter)
            required:
              - id
              - source_id
              - source_group
              - name
              - currency
              - price
              - quantity
              - price_amount
              - type_id
              - rate
              - revenue
              - source_currency
            properties:
              id:
                type: string
                description: Basket ID
                example: xxxxxxxxxxxxxxxxxxxx
                minLength: 20
                maxLength: 20
              source_id:
                type: string
                description: Basket ID from the network
                example: P8616497616
              source_group:
                type:
                  - 'null'
                  - string
                description: Group ID from the network (Affilinet)
              name:
                type: string
                description: Name of the product
                example: Samsung Galaxy S26
              currency:
                type: string
                description: Currency ISO 4217 code
                example: EUR
                minLength: 3
                maxLength: 3
              price:
                type: number
                description: Product price
                example: 561.42
              quantity:
                type: integer
                description: Quantity of product
                format: int32
                example: 1
              price_amount:
                type: number
                description: Amount price
                example: 561.42
              type_id:
                type: string
                description: Type ID
                enum:
                  - sale
                  - lead
                  - other
              rate:
                type:
                  - 'null'
                  - number
                description: Rate in percent if sale type
                example: 2.5
              revenue:
                type: number
                description: Basket revenue
                example: 14.04
              source_currency:
                type: object
                description: Currency source from network
                required:
                  - currency
                  - price
                  - price_amount
                  - revenue
                properties:
                  currency:
                    type: string
                    description: Currency ISO 4217 code
                    example: EUR
                    minLength: 3
                    maxLength: 3
                  price:
                    type: number
                    description: Product price
                    example: 499
                  price_amount:
                    type: number
                    description: Amount price
                    example: 499
                  revenue:
                    type: number
                    description: Basket revenue
                    example: 12.48
    Revenue:
      type: object
      required:
        - date
        - network_id
        - network_name
        - network_favicon
        - connection_id
        - connection_name
        - advertiser_id
        - advertiser_name
        - advertiser_favicon
        - country
        - channel_id
        - channel_name
        - status_id
        - currency
        - revenue
        - source_currency
        - transaction_count
        - click_count
      properties:
        day:
          type: string
          description: Date (date ISO 8601)
          format: date
          example: '2022-07-18'
        network_id:
          type: string
          description: Network ID
          example: xxxx
          minLength: 4
          maxLength: 4
        network_name:
          type: string
          description: Network name
          example: Awin
        network_favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://i.strackr.com/networks/favicons/awin.png
        connection_id:
          type: string
          description: Connection ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        connection_name:
          type: string
          description: Name of the connection (from user)
          example: Tradetracker NL
        advertiser_id:
          type:
            - 'null'
            - string
          description: Advertiser ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        advertiser_name:
          type:
            - 'null'
            - string
          description: Advertiser name
          example: Currys
        advertiser_favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://favicon.strackr.com/?sz=32&domain=url.com
        country:
          type: string
          description: Country ISO 3166-1 alpha-2 code
          example: GB
          minLength: 2
          maxLength: 2
        channel_id:
          type:
            - 'null'
            - string
          description: Channel ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        channel_name:
          type:
            - 'null'
            - string
          description: Channel name
          example: My website
        status_id:
          type: string
          description: Status ID
          enum:
            - pending
            - declined
            - confirmed
        currency:
          type: string
          description: Currency ISO 4217 code
          example: EUR
          minLength: 3
          maxLength: 3
        revenue:
          type: number
          description: Revenue from the transaction
          example: 1.78
        source_currency:
          type: object
          description: Currency source from network
          required:
            - currency
            - revenue
          properties:
            currency:
              type: string
              description: Currency ISO 4217 code
              example: GBP
              minLength: 3
              maxLength: 3
            revenue:
              type: number
              description: Revenue from the transaction
              example: 1.5
        transaction_count:
          type:
            - 'null'
            - integer
          description: Total transactions
          format: int32
          example: 1
        click_count:
          type:
            - 'null'
            - integer
          description: Total clicks (when there is CPC revenue)
          format: int32
          example: 298
    Click:
      type: object
      required:
        - date
        - network_id
        - network_name
        - network_favicon
        - connection_id
        - connection_name
        - advertiser_id
        - advertiser_name
        - advertiser_favicon
        - channel_id
        - channel_name
        - click_count
      properties:
        day:
          type: string
          description: Date (date ISO 8601)
          format: date
          example: '2022-07-18'
        network_id:
          type: string
          description: Network ID
          example: xxxx
          minLength: 4
          maxLength: 4
        network_name:
          type: string
          description: Network name
          example: Awin
        network_favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://i.strackr.com/networks/favicons/awin.png
        connection_id:
          type: string
          description: Connection ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        connection_name:
          type: string
          description: Name of the connection (from user)
          example: Tradetracker NL
        advertiser_id:
          type:
            - 'null'
            - string
          description: Advertiser ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        advertiser_name:
          type:
            - 'null'
            - string
          description: Advertiser name
          example: Currys
        advertiser_favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://favicon.strackr.com/?sz=32&domain=url.com
        channel_id:
          type:
            - 'null'
            - string
          description: Channel ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        channel_name:
          type:
            - 'null'
            - string
          description: Channel name
          example: My website
        click_count:
          format: int32
          description: Total clicks
          example: 298
    Payment:
      type: object
      required:
        - billed_at
        - paid_at
        - network_id
        - network_name
        - network_favicon
        - connection_id
        - connection_name
        - currency
        - amount
        - source_currency
      properties:
        billed_at:
          type: string
          description: Billed day (date ISO 8601)
          format: date
          example: '2022-07-18'
        paid_at:
          type:
            - 'null'
            - string
          description: Paid day (date ISO 8601)
          format: date
          example: '2022-07-18'
        network_id:
          type: string
          description: Network ID
          example: xxxx
          minLength: 4
          maxLength: 4
        network_name:
          type: string
          description: Network name
          example: Awin
        network_favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://i.strackr.com/networks/favicons/awin.png
        connection_id:
          type: string
          description: Connection ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        connection_name:
          type: string
          description: Name of the connection (from user)
          example: Tradetracker NL
        currency:
          type: string
          description: Currency ISO 4217 code
          example: GBP
          minLength: 3
          maxLength: 3
        amount:
          type: number
          description: Amount of the payment
          example: 84.15
        source_currency:
          type: object
          description: Currency source from network
          required:
            - currency
            - amount
          properties:
            currency:
              type: string
              description: Currency ISO 4217 code
              example: GBP
              minLength: 3
              maxLength: 3
            amount:
              type: number
              description: Amount of the payment
              example: 92.27
    Product:
      type: object
      required:
        - name
        - quantity
        - currency
        - order_amount
        - revenue
        - items
      properties:
        name:
          type: string
          description: Name of the product
          example: Samsung Galaxy S26
        quantity:
          type: integer
          description: Quantity of product
          format: int32
          example: 1
        currency:
          type: string
          description: Currency ISO 4217 code
          example: EUR
          minLength: 3
          maxLength: 3
        order_amount:
          type: number
          description: Amount of transactions
          example: 561.42
        revenue:
          type: number
          description: Revenue from transactions
          example: 14.04
        items:
          type: array
          items:
            type: object
            description: An item
            required:
              - network_id
              - network_name
              - network_favicon
              - advertiser_id
              - advertiser_name
              - advertiser_favicon
              - currency
              - order_amount
              - revenue
              - status_id
            properties:
              network_id:
                type: string
                description: Network ID
                example: xxxx
                minLength: 4
                maxLength: 4
              network_name:
                type: string
                description: Network name
                example: TradeTracker
              network_favicon:
                type:
                  - 'null'
                  - string
                description: Favicon URL (from `expand` parameter)
                format: uri
                example: https://i.strackr.com/networks/favicons/awin.png
              advertiser_id:
                type:
                  - 'null'
                  - string
                description: Advertiser ID
                example: xxxxxxxx
                minLength: 8
                maxLength: 8
              advertiser_name:
                type:
                  - 'null'
                  - string
                description: Advertiser name
                example: Expert.nl
              advertiser_favicon:
                type:
                  - 'null'
                  - string
                description: Favicon URL (from `expand` parameter)
                format: uri
                example: https://favicon.strackr.com/?sz=32&domain=url.com
              currency:
                type: string
                description: Currency ISO 4217 code
                example: EUR
                minLength: 3
                maxLength: 3
              order_amount:
                type: number
                description: Amount of the transaction
                example: 561.42
              revenue:
                type: number
                description: Revenue from the transaction
                example: 14.04
              status_id:
                type: string
                description: Status ID
                enum:
                  - pending
                  - declined
                  - confirmed
    StatisticDate:
      type:
        - 'null'
        - string
      description: Date, Week, Month, or Year, depending on the `time_range`
      example: '2022-08-07'
    StatisticRevenue:
      type:
        - 'null'
        - number
      description: Amount of the transaction (use `revenues` with `select` parameter)
      example: 561.42
    StatisticTransaction:
      type:
        - 'null'
        - object
      description: Transaction statistics (use `transactions` with `select` parameter)
      required:
        - count
        - revenue_amount
        - order_amount
      properties:
        count:
          type:
            - 'null'
            - integer
          description: Number of transaction
          format: int32
          example: 10
        revenue_amount:
          type:
            - 'null'
            - number
          description: Amount of revenue at transaction level
          example: 561.42
        order_amount:
          type:
            - 'null'
            - number
          description: Amount of order
          example: 561.42
    StatisticClick:
      type:
        - 'null'
        - integer
      description: Number of clicks (use `clicks` with `select` parameter)
      format: int32
      example: 10
    StatisticPayment:
      type:
        - 'null'
        - object
      description: Payment statistics (use `payments` with `select` parameter)
      required:
        - count
        - amount
      properties:
        count:
          type:
            - 'null'
            - integer
          description: Number of payment
          format: int32
          example: 10
        amount:
          type:
            - 'null'
            - number
          description: Amount of payment
          example: 561.42
    StatisticPerformance:
      type:
        - 'null'
        - object
      description: Performance statistics (use `performances` with `select` parameter)
      required:
        - rev_share
        - epc
        - cr
        - aov
      properties:
        rev_share:
          type:
            - 'null'
            - number
          description: Rev share
          example: 49.44
        epc:
          type:
            - 'null'
            - number
          description: Earning Per Click
          example: 2.9
        cr:
          type:
            - 'null'
            - number
          description: Convertion Rate
          example: 10.81
        aov:
          type:
            - 'null'
            - number
          description: Average Order Value
          example: 55.79
    Statistic:
      type: object
      required:
        - revenue_amount
        - transactions
        - click_count
        - payments
        - performances
      properties:
        revenue_amount:
          $ref: '#/components/schemas/StatisticRevenue'
        transactions:
          $ref: '#/components/schemas/StatisticTransaction'
        click_count:
          $ref: '#/components/schemas/StatisticClick'
        payments:
          $ref: '#/components/schemas/StatisticPayment'
        performances:
          $ref: '#/components/schemas/StatisticPerformance'
    StatisticStatus:
      type:
        - 'null'
        - object
      description: Status statistics (use `statuses` with `select` parameter)
      required:
        - confirmed
        - pending
        - declined
      properties:
        confirmed:
          type:
            - 'null'
            - object
          description: Statistics for confirmed transactions
          required:
            - revenue_amount
            - transaction_count
          properties:
            revenue_amount:
              type:
                - 'null'
                - number
              description: Amount of revenue
              example: 2.9
            transaction_count:
              type:
                - 'null'
                - integer
              description: Number of transaction
              format: int32
              example: 10
        pending:
          type:
            - 'null'
            - object
          description: Statistics for pending transactions
          required:
            - revenue_amount
            - transaction_count
          properties:
            revenue_amount:
              type:
                - 'null'
                - number
              description: Amount of revenue
              example: 2.9
            transaction_count:
              type:
                - 'null'
                - integer
              description: Number of transaction
              format: int32
              example: 10
        declined:
          type:
            - 'null'
            - object
          description: Statistics for declined transactions
          required:
            - revenue_amount
            - transaction_count
          properties:
            revenue_amount:
              type:
                - 'null'
                - number
              description: Amount of revenue
              example: 2.9
            transaction_count:
              type:
                - 'null'
                - integer
              description: Number of transaction
              format: int32
              example: 10
    StatisticNoPayment:
      type: object
      required:
        - revenue_amount
        - transactions
        - click_count
        - performances
      properties:
        revenue_amount:
          $ref: '#/components/schemas/StatisticRevenue'
        transactions:
          $ref: '#/components/schemas/StatisticTransaction'
        click_count:
          $ref: '#/components/schemas/StatisticClick'
        performances:
          $ref: '#/components/schemas/StatisticPerformance'
    StatisticDevice:
      type: object
      required:
        - transactions
        - performances
      properties:
        transactions:
          $ref: '#/components/schemas/StatisticTransaction'
        performances:
          $ref: '#/components/schemas/StatisticPerformance'
    StatisticCountry:
      type: object
      required:
        - revenue_amount
        - transactions
        - performances
      properties:
        revenue_amount:
          $ref: '#/components/schemas/StatisticRevenue'
        transactions:
          $ref: '#/components/schemas/StatisticTransaction'
        performances:
          $ref: '#/components/schemas/StatisticPerformance'
    Linkbuilder:
      type: object
      required:
        - url
        - advertisers
      properties:
        url:
          type: string
          description: Deepurl from the query parameter
          format: uri
          example: https://url.com/productpage
        advertisers:
          type: array
          items:
            type: object
            description: List of Advertisers
            required:
              - advertiser_id
              - advertiser_name
              - network_id
              - network_name
              - is_deeplink_enabled
              - connections
            properties:
              advertiser_id:
                type: string
                description: Advertiser ID
                example: xxxxxxxx
                minLength: 8
                maxLength: 8
              advertiser_name:
                type: string
                description: Advertiser name
                example: Expert.nl
              network_id:
                type: string
                description: Network ID
                example: xxxx
                minLength: 4
                maxLength: 4
              network_name:
                type: string
                description: Network name
                example: Awin
              is_deeplink_enabled:
                type: boolean
                description: Is deeplink enabled for this advertiser (see parameter `is_deeplink_enabled_only`)
              connections:
                type: array
                items:
                  type: object
                  description: List of connections
                  required:
                    - connection_id
                    - connection_name
                    - links
                  properties:
                    connection_id:
                      type: string
                      description: Connection ID
                      example: xxxxxxxx
                      minLength: 8
                      maxLength: 8
                    connection_name:
                      type: string
                      description: Name of the connection (from user)
                      example: Awin FR
                    links:
                      type: array
                      items:
                        type: object
                        description: List of links
                        required:
                          - channel_id
                          - channel_name
                          - commissions
                          - tracking_link
                        properties:
                          channel_id:
                            type:
                              - 'null'
                              - string
                            description: Channel ID
                            example: xxxxxxxx
                            minLength: 8
                            maxLength: 8
                          channel_name:
                            type:
                              - 'null'
                              - string
                            description: Channel name
                            example: My website
                          commissions:
                            $ref: '#/components/schemas/Commissions'
                          tracking_link:
                            type: string
                            description: Tracking link
                            format: uri
                            example: https://www.awin1.com/cread.php?url
    Trackinglink:
      type: object
      required:
        - advertiser_id
        - advertiser_name
        - network_id
        - network_name
        - is_deeplink_enabled
        - domains
        - connections
      properties:
        advertiser_id:
          type: string
          description: Advertiser ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        advertiser_name:
          type: string
          description: Advertiser name
          example: Expert.nl
        network_id:
          type: string
          description: Network ID
          example: xxxx
          minLength: 4
          maxLength: 4
        network_name:
          type: string
          description: Network name
          example: Awin
        is_deeplink_enabled:
          type: boolean
          description: Is deeplink enabled for this advertiser (see parameter `is_deeplink_enabled_only`)
        domains:
          type:
            - 'null'
            - array
          description: List of domains enabled for this advertiser (from `expand` parameter)
          items:
            type: string
        connections:
          type: array
          items:
            type: object
            description: List of connections
            required:
              - connection_id
              - connection_name
              - links
            properties:
              connection_id:
                type: string
                description: Connection ID
                example: xxxxxxxx
                minLength: 8
                maxLength: 8
              connection_name:
                type: string
                description: Name of the connection (from user)
                example: Awin FR
              links:
                type: array
                items:
                  type: object
                  description: List of links
                  required:
                    - channel_id
                    - channel_name
                    - commissions
                    - tracking_link
                  properties:
                    channel_id:
                      type:
                        - 'null'
                        - string
                      description: Channel ID
                      example: xxxxxxxx
                      minLength: 8
                      maxLength: 8
                    channel_name:
                      type:
                        - 'null'
                        - string
                      description: Channel name
                      example: My website
                    commissions:
                      $ref: '#/components/schemas/Commissions'
                    tracking_link:
                      type: string
                      description: Tracking link
                      format: uri
                      example: https://www.awin1.com/cread.php?url
    Commissiondetail:
      type: object
      required:
        - id
        - source_id
        - network_id
        - network_name
        - connection_id
        - connection_name
        - advertiser_id
        - advertiser_name
        - advertiser_favicon
        - channel_id
        - channel_name
        - name
        - description
        - status_id
        - country
        - currency
        - type_id
        - commission
        - commission_tiers
        - commission_conditions
        - start_at
        - end_at
        - cookie_hours
        - previous_commission
        - commission_changed_at
      properties:
        id:
          type: string
          description: Commission ID
          example: xxxxxxxxxxxx
          minLength: 12
          maxLength: 12
        source_id:
          type: string
          description: Commission ID from the network
          example: 2d97e1369d40ba86d9061ad767cd636b
        network_id:
          type: string
          description: Network ID
          example: xxxx
          minLength: 4
          maxLength: 4
        network_name:
          type: string
          description: Network name
          example: TradeTracker
        connection_id:
          type: string
          description: Connection ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        connection_name:
          type: string
          description: Name of the connection (from user)
          example: Tradetracker NL
        advertiser_id:
          type: string
          description: Advertiser ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        advertiser_name:
          type: string
          description: Advertiser name
          example: Expert.nl
        advertiser_favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://favicon.strackr.com/?sz=32&domain=url.com
        channel_id:
          type:
            - 'null'
            - string
          description: Channel ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        channel_name:
          type:
            - 'null'
            - string
          description: Channel name
          example: My website
        name:
          type: string
          description: Commission name
          example: General
        description:
          type:
            - 'null'
            - string
          description: Commission description
          example: Business computers
        status_id:
          type: string
          description: Status ID
          enum:
            - active
            - upcoming
            - expired
            - removed
        country:
          type:
            - 'null'
            - string
          description: Country ISO 3166-1 alpha-2 code
          example: NL
          minLength: 2
          maxLength: 2
        currency:
          type:
            - 'null'
            - string
          description: Currency ISO 4217 code
          example: EUR
          minLength: 3
          maxLength: 3
        type_id:
          type: string
          description: Type ID
          enum:
            - ratio
            - fixed
            - cpc
        commission:
          type: number
          description: Default commission
        commission_tiers:
          type:
            - 'null'
            - object
          description: Commission tiers with threshold
          required:
            - threshold_type_id
            - period_id
            - tiers
          properties:
            period_id:
              type:
                - 'null'
                - string
              description: Period ID
              enum:
                - month
            tiers:
              type: array
              items:
                type: object
                description: Tier detail
                required:
                  - name
                  - type_id
                  - threshold_type_id
                  - threshold
                  - commission
                properties:
                  name:
                    type:
                      - 'null'
                      - string
                    description: Tier name
                    example: General
                  type_id:
                    type: string
                    description: Type ID
                    enum:
                      - ratio
                      - fixed
                      - cpc
                  threshold_type_id:
                    type:
                      - 'null'
                      - string
                    description: Threshold type ID
                    enum:
                      - transaction_count
                      - revenue
                      - order_amount
                  threshold:
                    type: number
                    description: Threshold
                  commission:
                    type: number
                    description: Commission
        commission_conditions:
          type:
            - 'null'
            - array
          items:
            type: object
            description: Condition detail
            required:
              - name
              - type_id
              - commission
            properties:
              name:
                type:
                  - 'null'
                  - string
                description: Condition name
                example: General
              type_id:
                type: string
                description: Type ID
                enum:
                  - ratio
                  - fixed
                  - cpc
              commission:
                type: number
                description: Commission
        start_at:
          type:
            - 'null'
            - string
          description: Start datetime (date ISO 8601)
          format: date-time
          example: '2021-06-10T13:10:31'
        end_at:
          type:
            - 'null'
            - string
          description: End datetime (date ISO 8601)
          format: date-time
          example: '2023-05-23T03:55:54'
        cookie_hours:
          type:
            - 'null'
            - integer
          description: Cookie duration in hours
          format: int32
          example: 10
        previous_commission:
          type:
            - 'null'
            - number
          description: Previous commission
        commission_changed_at:
          type:
            - 'null'
            - string
          description: Commission change datetime (date ISO 8601) on Strackr
          format: date-time
          example: '2023-05-23T03:55:54Z'
    Deal:
      type: object
      required:
        - id
        - source_id
        - network_id
        - network_name
        - connection_id
        - connection_name
        - advertiser_id
        - advertiser_name
        - advertiser_favicon
        - advertiser_website
        - channel_id
        - channel_name
        - type_id
        - title
        - description
        - status_id
        - start_at
        - end_at
        - coupon
        - discount_ratio
        - discount_fixed
        - currency
        - country
        - deeplink
        - tracking_link
        - terms
      properties:
        id:
          type: string
          description: Deal ID
          example: xxxxxxxxxxxxxx
          minLength: 14
          maxLength: 14
        source_id:
          type: string
          description: Deal ID from the network
          example: 2d97e1369d40ba86d9061ad767cd636b
        network_id:
          type: string
          description: Network ID
          example: xxxx
          minLength: 4
          maxLength: 4
        network_name:
          type: string
          description: Network name
          example: TradeTracker
        connection_id:
          type: string
          description: Connection ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        connection_name:
          type: string
          description: Name of the connection (from user)
          example: Tradetracker NL
        advertiser_id:
          type:
            - 'null'
            - string
          description: Advertiser ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        advertiser_name:
          type:
            - 'null'
            - string
          description: Advertiser name
          example: Expert.nl
        advertiser_favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://favicon.strackr.com/?sz=32&domain=url.com
        advertiser_website:
          type:
            - 'null'
            - string
          description: Advertiser URL (from `expand` parameter)
          format: uri
          example: https://url.com
        channel_id:
          type:
            - 'null'
            - string
          description: Channel ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        channel_name:
          type:
            - 'null'
            - string
          description: Channel name
          example: My website
        type_id:
          type: string
          description: Type ID
          enum:
            - coupon
            - promotion
        title:
          type: string
          description: Title
          example: 'Mondo shopping: Code promo'
        description:
          type:
            - 'null'
            - string
          description: Description
          example: 'PROMO10: 10% de remise à partir de 50 EUR d’achat.'
        status_id:
          type: string
          description: Status ID
          enum:
            - active
            - upcoming
            - expired
            - removed
        start_at:
          type: string
          description: Start datetime (date ISO 8601)
          format: date-time
          example: '2021-06-10T13:10:31'
        end_at:
          type: string
          description: End datetime (date ISO 8601)
          format: date-time
          example: '2023-05-23T03:55:54'
        coupon:
          type:
            - 'null'
            - string
          description: Coupon code
          example: PROMO10
        discount_ratio:
          type:
            - 'null'
            - number
          description: Ratio discount (%)
        discount_fixed:
          type:
            - 'null'
            - number
          description: Fixed discount (EUR, USD, etc...)
        currency:
          type:
            - 'null'
            - string
          description: Currency ISO 4217 code
          example: EUR
          minLength: 3
          maxLength: 3
        country:
          type:
            - 'null'
            - string
          description: Country ISO 3166-1 alpha-2 code
          example: NL
          minLength: 2
          maxLength: 2
        deeplink:
          type:
            - 'null'
            - string
          description: Deep link to the deal
          format: uri
          example: https://url.com/productpage
        tracking_link:
          type:
            - 'null'
            - string
          description: Tracking link to the deal
          format: uri
          example: https://trackinglink.com/xyz
        terms:
          type:
            - 'null'
            - string
          description: Advertiser terms
    Productfeed:
      type: object
      required:
        - id
        - source_id
        - network_id
        - network_name
        - connection_id
        - connection_name
        - advertiser_id
        - advertiser_name
        - channel_id
        - channel_name
        - name
        - status_id
        - country
        - currency
        - url
        - url_type
        - mime_type
        - file_size
        - is_compressed
        - product_count
        - file_updated_at
      properties:
        id:
          type: string
          description: Product feed ID
          example: xxxxxxxxxxxx
          minLength: 12
          maxLength: 12
        source_id:
          type: string
          description: Product feed ID from the network
          example: 2d97e1369d40ba86d9061ad767cd636b
        network_id:
          type: string
          description: Network ID
          example: xxxx
          minLength: 4
          maxLength: 4
        network_name:
          type: string
          description: Network name
          example: TradeTracker
        connection_id:
          type: string
          description: Connection ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        connection_name:
          type: string
          description: Name of the connection (from user)
          example: Tradetracker NL
        advertiser_id:
          type: string
          description: Advertiser ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        advertiser_name:
          type: string
          description: Advertiser name
          example: Expert.nl
        channel_id:
          type:
            - 'null'
            - string
          description: Channel ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        channel_name:
          type:
            - 'null'
            - string
          description: Channel name
          example: My website
        name:
          type: string
          description: Product feed name
          example: General
        status_id:
          type: string
          description: Status ID
          enum:
            - active
            - removed
        country:
          type:
            - 'null'
            - string
          description: Country ISO 3166-1 alpha-2 code
          example: NL
          minLength: 2
          maxLength: 2
        currency:
          type:
            - 'null'
            - string
          description: Currency ISO 4217 code
          example: EUR
          minLength: 3
          maxLength: 3
        url:
          type: string
          description: URL to download the product feed file
          format: uri
        url_type:
          type: string
          description: URL Type ID
          enum:
            - http
            - sftp
        mime_type:
          type:
            - 'null'
            - string
          description: MIME Type
          enum:
            - application/csv
            - application/xml
        file_size:
          type:
            - 'null'
            - number
          description: File size in Ko
          format: int32
        is_compressed:
          type:
            - 'null'
            - boolean
          description: If the file is compressed
        product_count:
          type:
            - 'null'
            - number
          description: Product count in the file
          format: int32
        file_updated_at:
          type:
            - 'null'
            - string
          description: File updated datetime (date ISO 8601), information from the network
          format: date-time
          example: '2023-05-23T03:55:54Z'
    NetworkIM:
      type: object
      required:
        - id
        - name
        - logo
        - favicon
        - connections
      properties:
        id:
          type: string
          description: Network ID
          example: xxxx
          minLength: 4
          maxLength: 4
        name:
          type: string
          description: Network name
          example: Awin
        logo:
          type:
            - 'null'
            - string
          description: Logo URL (from `expand` parameter)
          format: uri
          example: https://i.strackr.com/networks/awin.png
        favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://i.strackr.com/networks/favicons/awin.png
        connections:
          type:
            - 'null'
            - array
          description: Connection list (from `expand` parameter)
          items:
            type: object
            required:
              - id
              - name
              - connector_id
              - connector_name
              - is_transaction_inquiries
              - is_inquiries
              - state
              - upload_type_id
              - upload_frequency
              - inquiry_fields
            properties:
              id:
                type: string
                description: Connection ID
                example: xxxxxxxx
                minLength: 8
                maxLength: 8
              name:
                type: string
                description: Name of the connection (from user)
                example: Awin FR
              connector_id:
                type: string
                description: Connector ID
                example: xxxxxx
                minLength: 6
                maxLength: 6
              connector_name:
                type: string
                description: Name of the connector
                example: API Transactions
              is_transaction_inquiries:
                type: boolean
                description: Transaction inquiry available
              is_inquiries:
                type: boolean
                description: Inquiries available
              state:
                type: string
                description: State ID
                enum:
                  - pending
                  - in_progress
                  - closed
                  - paused
              upload_type_id:
                type:
                  - 'null'
                  - string
                description: Upload type ID
                enum:
                  - api
                  - export_file
                  - ftp
              upload_frequency:
                type: string
                description: Upload frequency
                enum:
                  - manual
                  - daily
                  - weekly
                  - monthly
              inquiry_fields:
                type:
                  - 'null'
                  - object
                description: Inquiry fields
                required:
                  - available
                  - required
                properties:
                  available:
                    type: array
                    description: Available fields
                    items:
                      type: string
                      enum:
                        - connection_id
                        - advertiser_id
                        - channel_id
                        - type_id
                        - sold_at
                        - order_id
                        - customer_id
                        - order_amount
                        - order_amount
                        - revenue
                        - currency
                        - custom_1
                        - custom_2
                        - comment
                        - tracking_url
                  required:
                    type:
                      - 'null'
                      - array
                    description: Required fields
                    items:
                      type: string
                      enum:
                        - connection_id
                        - advertiser_id
                        - channel_id
                        - type_id
                        - sold_at
                        - order_id
                        - customer_id
                        - order_amount
                        - order_amount
                        - revenue
                        - currency
                        - custom_1
                        - custom_2
                        - comment
                        - tracking_url
    Transactioninquiry:
      type: object
      required:
        - id
        - source_id
        - network_id
        - network_name
        - network_favicon
        - connection_id
        - connection_name
        - advertiser_id
        - advertiser_name
        - advertiser_favicon
        - channel_id
        - channel_name
        - type_id
        - created_at
        - sold_at
        - order_id
        - customer_id
        - currency
        - expected_order_amount
        - expected_revenue
        - final_order_amount
        - final_revenue
        - source_currency
        - customs
        - status_id
        - status_updated_at
        - reason
        - comment
      properties:
        id:
          type: string
          description: Transaction ID
          example: xxxxxxxxxxxxxxxx
          minLength: 16
          maxLength: 16
        source_id:
          type:
            - 'null'
            - string
          description: Transaction ID from the network
          example: 861P64916
        network_id:
          type: string
          description: Network ID
          example: xxxx
          minLength: 4
          maxLength: 4
        network_name:
          type: string
          description: Network name
          example: TradeTracker
        network_favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://i.strackr.com/networks/favicons/awin.png
        connection_id:
          type: string
          description: Connection ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        connection_name:
          type: string
          description: Name of the connection (from user)
          example: Tradetracker NL
        advertiser_id:
          type:
            - 'null'
            - string
          description: Advertiser ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        advertiser_name:
          type:
            - 'null'
            - string
          description: Advertiser name
          example: Expert.nl
        advertiser_favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://favicon.strackr.com/?sz=32&domain=url.com
        channel_id:
          type:
            - 'null'
            - string
          description: Channel ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        channel_name:
          type:
            - 'null'
            - string
          description: Channel name
          example: My website
        type_id:
          type: string
          description: Type ID
          enum:
            - unknown
            - untracked
            - incorrect
            - declined
        created_at:
          type: string
          description: Transaction inquiry creation datetime (ISO 8601)
          format: date-time
          example: '2022-08-07T12:24:49'
        sold_at:
          type: string
          description: Transaction datetime (ISO 8601)
          format: date-time
          example: '2022-08-07T12:24:49'
        order_id:
          type: string
          description: Order ID from the advertiser
          example: 3872-0956-FR-1 PP
        customer_id:
          type:
            - 'null'
            - string
          description: Customer ID from the advertiser
          example: user-123456
        currency:
          type: string
          description: Currency ISO 4217 code
          example: EUR
          minLength: 3
          maxLength: 3
        expected_order_amount:
          type: number
          description: Expected publisher order amount for the transaction inquiry
          example: 561.42
        expected_revenue:
          type: number
          description: Expected publisher commission for the transaction inquiry
          example: 14.04
        final_order_amount:
          type:
            - 'null'
            - number
          description: The order amount decided on by the advertiser
          example: 561.42
        final_revenue:
          type:
            - 'null'
            - number
          description: The revenue decided on by the advertiser
          example: 14.04
        source_currency:
          type: object
          description: Currency source information from network
          required:
            - currency
            - expected_order_amount
            - expected_revenue
            - final_order_amount
            - final_revenue
          properties:
            currency:
              type: string
              description: Currency ISO 4217 code
              example: EUR
              minLength: 3
              maxLength: 3
            expected_order_amount:
              type: number
              description: Expected publisher order amount for the transaction inquiry
              example: 561.42
            expected_revenue:
              type: number
              description: Expected publisher commission for the transaction inquiry
              example: 14.04
            final_order_amount:
              type:
                - 'null'
                - number
              description: The order amount decided on by the advertiser
              example: 561.42
            final_revenue:
              type:
                - 'null'
                - number
              description: The revenue decided on by the advertiser
              example: 14.04
        customs:
          type: array
          description: Customs IDs, for example SubIds. You can get 2 customs maximum.
          minItems: 2
          maxItems: 2
          items:
            type: string
            description: A Custom ID
            example: MY_CUSTOMS_IDS
        status_id:
          type: string
          description: Status ID
          enum:
            - pending
            - declined
            - confirmed
        status_updated_at:
          type:
            - 'null'
            - string
          description: Status updated datetime (ISO 8601), from pending to declined or confirmed on Strackr
          format: date-time
          example: '2022-08-24T14:57:42'
        reason:
          type:
            - 'null'
            - object
          description: Reason for the declined the transaction inquiry if provided
          required:
            - id
            - name
          properties:
            id:
              type: string
              description: Reason ID
              example: XXXXXXXX
              minLength: 8
              maxLength: 8
            name:
              type: string
              description: Reason name
              example: Order not complete - Customer cancelled order
        comment:
          type:
            - 'null'
            - string
          description: Comment from the network or the advertiser
          example: Transaction declined without reason
    Upload:
      type: object
      required:
        - id
        - network_id
        - network_name
        - network_favicon
        - connection_id
        - connection_name
        - type_id
        - status_id
        - range_start_date
        - range_end_date
        - sending_datetime
        - created_at
        - uploaded_at
        - inquiry_count
      properties:
        id:
          type: string
          description: Upload ID
          example: xxxxx
          minLength: 5
          maxLength: 5
        network_id:
          type: string
          description: Network ID
          example: xxxx
          minLength: 4
          maxLength: 4
        network_name:
          type: string
          description: Network name
          example: TradeDoubler
        network_favicon:
          type:
            - 'null'
            - string
          description: Favicon URL
          format: uri
          example: https://i.strackr.com/merchants/amazon-fr.png
        connection_id:
          type: string
          description: Connection ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        connection_name:
          type: string
          description: Connection name
          example: My connection
        type_id:
          type: string
          description: Type ID
          enum:
            - api
            - ftp
            - export_file
        status_id:
          type: string
          description: Status ID
          enum:
            - pending
            - upload_in_progress
            - ready_to_download
            - uploaded
        range_start_date:
          type:
            - 'null'
            - string
          description: Start range date (date ISO 8601)
          format: date
          example: '2023-08-24'
        range_end_date:
          type:
            - 'null'
            - string
          description: End range date (date ISO 8601)
          format: date
          example: '2023-08-28'
        sending_datetime:
          type:
            - 'null'
            - string
          description: Sending datetime (date ISO 8601)
          format: date-time
          example: 2023-08-24T23:01:02+02:00Z
        created_at:
          type: string
          description: Created datetime on Strackr (date ISO 8601)
          format: date-time
          example: 2023-08-22T21:00:01+02:00Z
        uploaded_at:
          type:
            - 'null'
            - string
          description: Uploaded datetime to the network (ISO 8601)
          format: date-time
          example: 2023-08-24T23:01:02+02:00Z
        inquiry_count:
          type:
            - 'null'
            - integer
          description: Total of uploaded inquiries
          format: int32
          example: 2746
    inquiry:
      type: object
      required:
        - id
        - network_id
        - network_name
        - network_favicon
        - connection_id
        - connection_name
        - advertiser_id
        - advertiser_name
        - advertiser_favicon
        - channel_id
        - channel_name
        - type_id
        - created_at
        - sold_at
        - order_id
        - customer_id
        - currency
        - order_amount
        - revenue
        - source_currency
        - customs
        - comment
        - tracking_url
        - status_id
        - inquiry_upload_id
        - inquiry_uploaded_at
        - error_message
      properties:
        id:
          type: string
          description: Inquiry ID
          example: xxxxxxxxxxxxxxxx
          minLength: 16
          maxLength: 16
        network_id:
          type: string
          description: Network ID
          example: xxxx
          minLength: 4
          maxLength: 4
        network_name:
          type: string
          description: Network name
          example: TradeTracker
        network_favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://i.strackr.com/networks/favicons/awin.png
        connection_id:
          type: string
          description: Connection ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        connection_name:
          type: string
          description: Name of the connection (from user)
          example: Tradetracker NL
        advertiser_id:
          type:
            - 'null'
            - string
          description: Advertiser ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        advertiser_name:
          type:
            - 'null'
            - string
          description: Advertiser name
          example: Expert.nl
        advertiser_favicon:
          type:
            - 'null'
            - string
          description: Favicon URL (from `expand` parameter)
          format: uri
          example: https://favicon.strackr.com/?sz=32&domain=url.com
        channel_id:
          type:
            - 'null'
            - string
          description: Channel ID
          example: xxxxxxxx
          minLength: 8
          maxLength: 8
        channel_name:
          type:
            - 'null'
            - string
          description: Channel name
          example: My website
        type_id:
          type: string
          description: Type ID
          enum:
            - unknown
            - untracked
            - incorrect
            - declined
        created_at:
          type: string
          description: Transaction inquiry creation datetime (ISO 8601)
          format: date-time
          example: '2022-08-07T12:24:49Z'
        sold_at:
          type: string
          description: Transaction datetime (ISO 8601)
          format: date-time
          example: '2022-08-07T12:24:49'
        order_id:
          type: string
          description: Order ID from the advertiser
          example: 3872-0956-FR-1 PP
        customer_id:
          type:
            - 'null'
            - string
          description: Customer ID from the advertiser
          example: user-123456
        currency:
          type: string
          description: Currency ISO 4217 code
          example: EUR
          minLength: 3
          maxLength: 3
        order_amount:
          type: number
          description: Order amount for the inquiry
          example: 561.42
        revenue:
          type: number
          description: Revenue for the inquiry
          example: 14.04
        source_currency:
          type: object
          description: Currency source information from network
          required:
            - currency
            - order_amount
            - revenue
          properties:
            currency:
              type: string
              description: Currency ISO 4217 code
              example: EUR
              minLength: 3
              maxLength: 3
            order_amount:
              type: number
              description: Order amount for the inquiry
              example: 561.42
            revenue:
              type: number
              description: Revenue for the inquiry
              example: 14.04
        customs:
          type: array
          description: Customs IDs, for example SubIds. You can get 2 customs maximum.
          minItems: 2
          maxItems: 2
          items:
            type: string
            description: A Custom ID
            example: MY_CUSTOMS_IDS
        comment:
          type:
            - 'null'
            - string
          description: Comment from publisher
          example: Transaction declined without reason
        tracking_url:
          type:
            - 'null'
            - string
          description: Tracking URL used for the transaction
          format: uri
        status_id:
          type: string
          description: Status ID
          enum:
            - pending
            - uploaded
            - error
        inquiry_upload_id:
          type:
            - 'null'
            - string
          description: Upload  ID
          example: xxxx
          minLength: 4
          maxLength: 4
        inquiry_uploaded_at:
          type:
            - 'null'
            - string
          description: Uploaded datetime to the network (ISO 8601)
          format: date-time
          example: 2023-08-24T23:01:02+02:00Z
        error_message:
          type:
            - 'null'
            - string
          description: Error message from the network
    StatisticDateIM:
      type:
        - 'null'
        - string
      description: Date, Week, Month, or Year, depending on the `time_range`
      example: '2024-08-07'
    StatisticTransactionInquiry:
      type:
        - 'null'
        - object
      description: Transaction inquiry statistics (use `transaction_inquiries` with `select` parameter)
      required:
        - count
        - revenue_amount
        - order_amount
      properties:
        count:
          type:
            - 'null'
            - integer
          description: Number of transaction inquiry
          format: int32
          example: 10
        revenue_amount:
          type:
            - 'null'
            - number
          description: Amount of revenue at transaction inquiry level
          example: 561.42
        order_amount:
          type:
            - 'null'
            - number
          description: Amount of order
          example: 561.42
    StatisticInquiry:
      type:
        - 'null'
        - object
      description: Inquiry statistics (use `inquiries` with `select` parameter)
      required:
        - count
        - revenue_amount
        - order_amount
      properties:
        count:
          type:
            - 'null'
            - integer
          description: Number of inquiry
          format: int32
          example: 10
        revenue_amount:
          type:
            - 'null'
            - number
          description: Amount of revenue at inquiry level
          example: 561.42
        order_amount:
          type:
            - 'null'
            - number
          description: Amount of order
          example: 561.42
    StatisticInquiryUpload:
      type:
        - 'null'
        - object
      description: Upload statistics (use `inquiry_uploads` with `select` parameter)
      required:
        - count
        - inquiry_count
      properties:
        count:
          type:
            - 'null'
            - integer
          description: Number of upload
          format: int32
          example: 10
        inquiry_count:
          type:
            - 'null'
            - number
          description: Number of inquiry uploaded
          format: int32
          example: 10
    StatisticTransactionInquiryStatus:
      type:
        - 'null'
        - object
      description: Transaction inquiry status statistics (use `transaction_inquiry_statuses` with `select` parameter)
      required:
        - confirmed
        - pending
        - declined
      properties:
        confirmed:
          type:
            - 'null'
            - object
          description: Statistics for confirmed transaction inquiries
          required:
            - count
            - revenue_amount
          properties:
            count:
              type:
                - 'null'
                - integer
              description: Number of transaction inquiry
              format: int32
              example: 10
            revenue_amount:
              type:
                - 'null'
                - number
              description: Amount of revenue
              example: 2.9
        pending:
          type:
            - 'null'
            - object
          description: Statistics for pending transaction inquiries
          required:
            - count
            - revenue_amount
          properties:
            count:
              type:
                - 'null'
                - integer
              description: Number of transaction inquiry
              format: int32
              example: 10
            revenue_amount:
              type:
                - 'null'
                - number
              description: Amount of revenue
              example: 2.9
        declined:
          type:
            - 'null'
            - object
          description: Statistics for declined transaction inquiries
          required:
            - count
            - revenue_amount
          properties:
            count:
              type:
                - 'null'
                - integer
              description: Number of transaction inquiry
              format: int32
              example: 10
            revenue_amount:
              type:
                - 'null'
                - number
              description: Amount of revenue
              example: 2.9
    StatisticInquiryStatus:
      type:
        - 'null'
        - object
      description: Inquiry status statistics (use `inquiry_statuses` with `select` parameter)
      required:
        - pending
        - uploaded
        - error
      properties:
        pending:
          type:
            - 'null'
            - integer
          description: Number of inquiry
          format: int32
          example: 10
        uploaded:
          type:
            - 'null'
            - integer
          description: Number of inquiry
          format: int32
          example: 10
        error:
          type:
            - 'null'
            - integer
          description: Number of inquiry
          format: int32
          example: 10
    StatisticInquiryUploadStatus:
      type:
        - 'null'
        - object
      description: Inquiry upload statistics (use `inquiry_upload_statuses` with `select` parameter)
      required:
        - pending
        - uploaded
        - upload_in_progress
        - ready_to_download
      properties:
        pending:
          type:
            - 'null'
            - integer
          description: Number of inquiries pending processing
          format: int32
          example: 10
        uploaded:
          type:
            - 'null'
            - integer
          description: Number of successfully uploaded inquiries
          format: int32
          example: 10
        upload_in_progress:
          type:
            - 'null'
            - integer
          description: Number of inquiries with an upload in progress
          format: int32
          example: 10
        ready_to_download:
          type:
            - 'null'
            - integer
          description: Number of inquiries available for download
          format: int32
          example: 10
    StatisticIM:
      type: object
      required:
        - transaction_inquiries
        - inquiries
        - inquiry_uploads
        - transaction_inquiry_statuses
        - inquiry_statuses
        - inquiry_upload_statuses
      properties:
        transaction_inquiries:
          $ref: '#/components/schemas/StatisticTransactionInquiry'
        inquiries:
          $ref: '#/components/schemas/StatisticInquiry'
        inquiry_uploads:
          $ref: '#/components/schemas/StatisticInquiryUpload'
        transaction_inquiry_statuses:
          $ref: '#/components/schemas/StatisticTransactionInquiryStatus'
        inquiry_statuses:
          $ref: '#/components/schemas/StatisticInquiryStatus'
        inquiry_upload_statuses:
          $ref: '#/components/schemas/StatisticInquiryUploadStatus'
    StatisticNoInquiryUpload:
      type: object
      required:
        - transaction_inquiries
        - inquiries
        - transaction_inquiry_statuses
        - inquiry_statuses
      properties:
        transaction_inquiries:
          $ref: '#/components/schemas/StatisticTransactionInquiry'
        inquiries:
          $ref: '#/components/schemas/StatisticInquiry'
        transaction_inquiry_statuses:
          $ref: '#/components/schemas/StatisticTransactionInquiryStatus'
        inquiry_statuses:
          $ref: '#/components/schemas/StatisticInquiryStatus'
    ADTransaction:
      $ref: '#/components/schemas/Transaction'
    ADRevenue:
      $ref: '#/components/schemas/Revenue'
  securitySchemes:
    apiIdQuery:
      type: apiKey
      in: query
      name: api_id
      description: Your API ID
    apiKeyQuery:
      type: apiKey
      in: query
      name: api_key
      description: Your API Key
  responses:
    UnexpectedError:
      description: unexpected error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    UnauthorizedError:
      description: Authentication credentials were missing or incorrect
      content:
        application/json:
          schema:
            type: object
            properties:
              detail:
                type: string
                example: Authentication failed
x-tagGroups:
  - name: Common
    tags:
      - Common Networks
      - Common Advertisers
      - Common Categories
      - Common Views
      - Common Ping
  - name: Affiliate Dashboard
    tags:
      - AD Reports
      - AD Statistics
      - AD Performances
      - AD Tools
  - name: Inquiry Manager
    tags:
      - IM Networks
      - IM Transaction inquiries
      - IM Inquiries
      - IM Uploads
      - IM Statistics
