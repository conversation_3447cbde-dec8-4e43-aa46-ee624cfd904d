#!/usr/bin/env python3
"""
Local testing script for Strackr integration using environment variables.
No Google Cloud authentication required.

Usage:
    1. Copy .env.example to .env
    2. Fill in your Strackr API credentials in .env
    3. Run: python test_strackr_local.py
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# Load environment variables from .env file
try:
    from dotenv import load_dotenv

    load_dotenv()
    print("✅ Loaded environment variables from .env file")
except ImportError:
    print(
        "⚠️  python-dotenv not installed. Please install with: pip install python-dotenv"
    )
    print("⚠️  Or set environment variables manually")

# Add the dags directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "dags"))

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def check_environment():
    """Check if required environment variables are set."""
    print("\n" + "=" * 50)
    print("CHECKING ENVIRONMENT")
    print("=" * 50)

    required_vars = ["STRACKR_API_ID", "STRACKR_API_KEY"]
    missing_vars = []

    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * len(value)}")  # Hide actual value
        else:
            print(f"❌ {var}: Not set")
            missing_vars.append(var)

    if missing_vars:
        print(f"\n❌ Missing required environment variables: {missing_vars}")
        print("\n📝 To fix this:")
        print("1. Copy .env.example to .env")
        print("2. Edit .env and add your Strackr API credentials")
        print("3. Run this script again")
        return False

    print("✅ All required environment variables are set")
    return True


def test_local_authentication():
    """Test local authentication using environment variables."""
    print("\n" + "=" * 50)
    print("TESTING LOCAL AUTHENTICATION")
    print("=" * 50)

    try:
        from dependencies.transaction_reports.strackr_auth_local import StrackrAuthLocal

        auth = StrackrAuthLocal()
        print("✅ StrackrAuthLocal initialized successfully")

        # Test credential validation
        is_valid = auth.validate_credentials()
        if is_valid:
            print("✅ Credentials are valid")

            # Show auth params structure (without exposing values)
            auth_params = auth.get_auth_params()
            print(f"✅ Auth params structure: {list(auth_params.keys())}")

            return True
        else:
            print("❌ Credentials are invalid")
            return False

    except Exception as e:
        print(f"❌ Authentication test failed: {str(e)}")
        return False


def test_local_api_client():
    """Test local API client."""
    print("\n" + "=" * 50)
    print("TESTING LOCAL API CLIENT")
    print("=" * 50)

    try:
        from dependencies.transaction_reports.strackr_client_local import (
            StrackrClientLocal,
        )

        client = StrackrClientLocal()
        print("✅ StrackrClientLocal initialized successfully")

        # Test with a small recent date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=7)  # Last 7 days

        start_str = start_date.strftime("%Y-%m-%dT00:00:00Z")
        end_str = end_date.strftime("%Y-%m-%dT23:59:59Z")

        print(f"📅 Testing date range: {start_str} to {end_str}")

        # Fetch with small limit for testing
        transactions = client.get_transaction_inquiries(
            start_date=start_str, end_date=end_str, limit=10  # Small limit for testing
        )

        print(f"✅ API call successful - retrieved {len(transactions)} transactions")

        if transactions:
            sample = transactions[0]
            print(f"📄 Sample transaction fields: {list(sample.keys())[:10]}...")
            print(f"📄 Sample transaction ID: {sample.get('id', 'N/A')}")
            print(
                f"📄 Sample order amount: {sample.get('expected_order_amount', 'N/A')}"
            )
            print(f"📄 Sample currency: {sample.get('currency', 'N/A')}")
            print(f"📄 Sample status: {sample.get('status_id', 'N/A')}")
        else:
            print("ℹ️  No transactions found in the date range (this might be normal)")

        return True, transactions

    except Exception as e:
        print(f"❌ API client test failed: {str(e)}")
        print(f"💡 This might be due to:")
        print(f"   - Invalid API credentials")
        print(f"   - Network connectivity issues")
        print(f"   - Strackr API being down")
        return False, []


def test_data_transformation(raw_transactions):
    """Test data transformation."""
    print("\n" + "=" * 50)
    print("TESTING DATA TRANSFORMATION")
    print("=" * 50)

    try:
        from dependencies.transaction_reports.strackr_transform import (
            transform_strackr_transactions,
        )

        if not raw_transactions:
            print("⚠️  No raw transactions to transform - skipping transformation test")
            return True, []

        print(f"🔄 Transforming {len(raw_transactions)} raw transactions...")

        normalized = transform_strackr_transactions(raw_transactions)

        print(
            f"✅ Transformation successful - {len(normalized)} normalized transactions"
        )

        if normalized:
            sample = normalized[0]
            print(f"📄 Sample normalized transaction:")
            print(f"   - ID: {sample.transaction_id}")
            print(f"   - Platform: {sample.platform}")
            print(f"   - Currency: {sample.currency}")
            print(f"   - Order Amount: {sample.order_amount}")
            print(f"   - Commission: {sample.commission_amount}")
            print(f"   - Status: {sample.status}")
            print(f"   - Merchant: {sample.merchant_name}")
            print(f"   - Date: {sample.transaction_date}")

        return True, normalized

    except Exception as e:
        print(f"❌ Transformation test failed: {str(e)}")
        return False, []


def test_data_validation(normalized_transactions):
    """Test data validation."""
    print("\n" + "=" * 50)
    print("TESTING DATA VALIDATION")
    print("=" * 50)

    try:
        from dependencies.transaction_reports.utils import (
            validate_transaction_data_quality,
        )

        if not normalized_transactions:
            print(
                "⚠️  No normalized transactions to validate - skipping validation test"
            )
            return True

        print(f"🔍 Validating {len(normalized_transactions)} transactions...")

        validation_results = validate_transaction_data_quality(
            normalized_transactions, platform="strackr"
        )

        print(f"✅ Validation complete")
        print(f"📊 Results:")
        print(f"   - Valid: {validation_results['is_valid']}")
        print(f"   - Transaction count: {validation_results['transaction_count']}")
        print(f"   - Errors: {len(validation_results['errors'])}")
        print(f"   - Warnings: {len(validation_results['warnings'])}")

        if validation_results["errors"]:
            print(f"❌ Errors found: {validation_results['errors']}")

        if validation_results["warnings"]:
            print(f"⚠️  Warnings: {validation_results['warnings']}")

        # Show metrics
        metrics = validation_results.get("metrics", {})
        if metrics:
            print(f"📈 Metrics:")
            print(
                f"   - Total commission: {metrics.get('total_commission_amount', 0):.2f}"
            )
            print(f"   - Total order value: {metrics.get('total_order_value', 0):.2f}")
            print(
                f"   - Average commission: {metrics.get('average_commission', 0):.2f}"
            )
            print(f"   - Status distribution: {metrics.get('status_distribution', {})}")

        return True

    except Exception as e:
        print(f"❌ Validation test failed: {str(e)}")
        return False


def run_local_tests():
    """Run the complete local test suite."""
    print("🚀 Starting Strackr Local Integration Tests")
    print("This uses environment variables instead of Google Secret Manager")
    print("=" * 60)

    # Check environment first
    if not check_environment():
        return False

    test_results = []

    # Test 1: Local Authentication
    test_results.append(test_local_authentication())

    # Test 2: Local API Client
    api_success, raw_transactions = test_local_api_client()
    test_results.append(api_success)

    # Test 3: Data Transformation
    transform_success, normalized_transactions = test_data_transformation(
        raw_transactions
    )
    test_results.append(transform_success)

    # Test 4: Data Validation
    test_results.append(test_data_validation(normalized_transactions))

    # Summary
    print("\n" + "=" * 60)
    passed_tests = sum(test_results)
    total_tests = len(test_results)

    if passed_tests == total_tests:
        print(f"🎉 ALL LOCAL TESTS PASSED ({passed_tests}/{total_tests})")
        print("✅ Strackr integration is working correctly!")
        print("🚀 Ready to test with Secret Manager or deploy to Airflow")
        return True
    else:
        print(f"❌ SOME TESTS FAILED ({passed_tests}/{total_tests})")
        print("🔧 Fix the issues before proceeding")
        return False


if __name__ == "__main__":
    success = run_local_tests()
    sys.exit(0 if success else 1)
