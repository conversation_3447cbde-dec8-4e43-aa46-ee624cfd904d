#!/usr/bin/env python3
"""
Simple test for data transformation without Google Cloud dependencies.
Tests the transformation logic using sample Strackr data.
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional

# Sample Strackr transaction data (based on the OpenAPI spec)
SAMPLE_STRACKR_DATA = {
    "id": "AAAAAAAAAAAAAA",
    "source_id": "XXXXXXXXXX",
    "network_id": "QQQQ",
    "network_name": "Impact Radius",
    "network_favicon": None,
    "connection_id": "XXXXXX",
    "connection_name": "Impact",
    "advertiser_id": "ZZZZZZ",
    "advertiser_name": "Temu Affiliate Program",
    "advertiser_favicon": None,
    "channel_id": None,
    "channel_name": None,
    "type_id": "untracked",
    "created_at": "2025-01-07T23:25:47",
    "sold_at": "2025-01-01T14:10:40",
    "order_id": "PO-069-10498291110952304",
    "customer_id": "USER_ID",
    "currency": "EUR",
    "expected_order_amount": 35.84,
    "expected_revenue": 8.96,
    "final_order_amount": None,
    "final_revenue": None,
    "source_currency": {
        "currency": "EUR",
        "expected_order_amount": 35.84,
        "expected_revenue": 8.96,
        "final_order_amount": None,
        "final_revenue": None
    },
    "customs": ["custom_id123", None],
    "status_id": "declined",
    "status_updated_at": "2025-01-12T00:18:31",
    "reason": {
        "id": "XXXXXXX",
        "name": "Another market channel was credited with this sale."
    },
    "comment": "Transaction declined without reason"
}

def parse_datetime(date_string: Optional[str]) -> Optional[datetime]:
    """Parse datetime string from Strackr API."""
    if not date_string:
        return None
    
    try:
        # Handle different datetime formats
        if date_string.endswith('Z'):
            return datetime.fromisoformat(date_string.replace('Z', '+00:00'))
        elif '+' in date_string or date_string.endswith('00'):
            return datetime.fromisoformat(date_string)
        else:
            # Assume UTC if no timezone info
            return datetime.fromisoformat(date_string + '+00:00')
    except Exception as e:
        print(f"Failed to parse datetime '{date_string}': {str(e)}")
        return None

def normalize_status(strackr_status: str) -> str:
    """Normalize Strackr status to standard status values."""
    status_mapping = {
        'pending': 'pending',
        'confirmed': 'confirmed',
        'declined': 'declined'
    }
    return status_mapping.get(strackr_status.lower(), strackr_status.lower())

def transform_strackr_transaction_simple(strackr_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Transform a single Strackr transaction to normalized schema.
    Simple version without dependencies.
    """
    try:
        # Extract core identifiers
        source_id = strackr_data['id']
        transaction_id = f"strackr_{source_id}"
        
        # Parse dates
        transaction_date = parse_datetime(strackr_data['sold_at'])
        created_date = parse_datetime(strackr_data['created_at'])
        last_updated = parse_datetime(strackr_data.get('status_updated_at')) or created_date
        
        # Extract financial data
        currency = strackr_data['currency']
        order_amount = float(strackr_data['expected_order_amount'])
        commission_amount = float(strackr_data['expected_revenue'])
        
        # Final amounts (if confirmed)
        final_order_amount = None
        final_commission_amount = None
        if strackr_data.get('final_order_amount') is not None:
            final_order_amount = float(strackr_data['final_order_amount'])
        if strackr_data.get('final_revenue') is not None:
            final_commission_amount = float(strackr_data['final_revenue'])
        
        # Extract merchant/network info
        network_name = strackr_data['network_name']
        merchant_name = strackr_data.get('advertiser_name') or 'Unknown'
        merchant_id = strackr_data.get('advertiser_id')
        connection_name = strackr_data.get('connection_name')
        
        # Extract status information
        status = normalize_status(strackr_data['status_id'])
        transaction_type = strackr_data.get('type_id')
        
        # Extract decline reason if present
        decline_reason = None
        if strackr_data.get('reason') and isinstance(strackr_data['reason'], dict):
            decline_reason = strackr_data['reason'].get('name')
        
        # Extract additional metadata
        channel_name = strackr_data.get('channel_name')
        comments = strackr_data.get('comment')
        
        # Build custom fields with Strackr-specific data
        custom_fields = {
            'network_id': strackr_data.get('network_id'),
            'connection_id': strackr_data.get('connection_id'),
            'source_currency': strackr_data.get('source_currency'),
            'customs': strackr_data.get('customs', []),
            'type_id': strackr_data.get('type_id')
        }
        
        # Create normalized transaction
        normalized = {
            'transaction_id': transaction_id,
            'platform': 'strackr',
            'source_transaction_id': source_id,
            
            'currency': currency,
            'order_amount': order_amount,
            'commission_amount': commission_amount,
            'final_order_amount': final_order_amount,
            'final_commission_amount': final_commission_amount,
            
            'order_id': strackr_data['order_id'],
            'customer_id': strackr_data.get('customer_id'),
            'transaction_date': transaction_date.isoformat() if transaction_date else None,
            'created_date': created_date.isoformat() if created_date else None,
            
            'network_name': network_name,
            'merchant_name': merchant_name,
            'merchant_id': merchant_id,
            'connection_name': connection_name,
            
            'status': status,
            'transaction_type': transaction_type,
            'decline_reason': decline_reason,
            
            'channel_name': channel_name,
            'custom_fields': custom_fields,
            'comments': comments,
            'last_updated': last_updated.isoformat() if last_updated else None
        }
        
        return normalized
        
    except Exception as e:
        print(f"Failed to transform Strackr transaction {strackr_data.get('id', 'unknown')}: {str(e)}")
        return None

def test_transformation():
    """Test the transformation function."""
    print("🚀 Testing Strackr Data Transformation")
    print("=" * 50)
    
    print("📄 Sample Strackr data:")
    print(json.dumps(SAMPLE_STRACKR_DATA, indent=2)[:500] + "...")
    
    print("\n🔄 Transforming data...")
    
    normalized = transform_strackr_transaction_simple(SAMPLE_STRACKR_DATA)
    
    if normalized:
        print("✅ Transformation successful!")
        print("\n📄 Normalized transaction:")
        print(json.dumps(normalized, indent=2))
        
        print("\n📊 Validation:")
        required_fields = [
            'transaction_id', 'platform', 'source_transaction_id',
            'currency', 'order_amount', 'commission_amount',
            'order_id', 'network_name', 'merchant_name', 'status'
        ]
        
        missing_fields = [field for field in required_fields if field not in normalized or normalized[field] is None]
        
        if missing_fields:
            print(f"❌ Missing required fields: {missing_fields}")
            return False
        else:
            print("✅ All required fields present")
        
        print(f"\n📈 Summary:")
        print(f"   - Transaction ID: {normalized['transaction_id']}")
        print(f"   - Platform: {normalized['platform']}")
        print(f"   - Currency: {normalized['currency']}")
        print(f"   - Order Amount: {normalized['order_amount']}")
        print(f"   - Commission: {normalized['commission_amount']}")
        print(f"   - Status: {normalized['status']}")
        print(f"   - Merchant: {normalized['merchant_name']}")
        
        return True
    else:
        print("❌ Transformation failed")
        return False

if __name__ == "__main__":
    success = test_transformation()
    if success:
        print("\n🎉 Transformation test passed!")
        print("✅ Ready to integrate with full pipeline")
    else:
        print("\n❌ Transformation test failed")
    
    exit(0 if success else 1)
